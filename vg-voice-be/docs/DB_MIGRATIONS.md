# Database Migration Guide for Vegrow Voice BE

This document explains how to set up, manage, and run database migrations for the FastAPI backend using Alembic and SQLAlchemy.

---

## 1. Requirements

- MySQL database (local or remote)
- Python virtual environment activated
- Install dependencies:
    ```sh
    pip install -r requirements.txt
    ```

---

## 2. Environment Variables

Set these variables in your shell or Docker Compose (no .env file required):
- `MYSQL_HOST`
- `MYSQL_PORT`
- `MYSQL_USER`
- `MYSQL_PASSWORD`
- `MYSQL_DATABASE`

Example:
```sh
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USER=root
export MYSQL_PASSWORD=password
export MYSQL_DATABASE=vg_voice
```

---

## 3. Alembic Setup

- Alembic is initialized in the `migrations/` folder.
- Alembic reads DB connection info from environment variables (see above).
- All models are defined in `app/models/` and imported in `migrations/env.py`.

---

## 4. Common Migration Commands

- **Create a new migration after changing models:**
    ```sh
    alembic revision --autogenerate -m "<your message>"
    ```
- **Apply all migrations to the database:**
    ```sh
    alembic upgrade head
    ```
- **Downgrade last migration:**
    ```sh
    alembic downgrade -1
    ```
- **Check migration status:**
    ```sh
    alembic current
    ```

---

## 5. Initial Setup Flow

1. Activate your Python virtual environment:
    ```sh
    source venv/bin/activate
    ```
2. Set the required environment variables (see above).
3. Ensure your DB exists (create manually if needed):
    ```sh
    mysql -u root -p -e "CREATE DATABASE vg_voice;"
    ```
4. Run all migrations:
    ```sh
    alembic upgrade head
    ```

---

## 6. Adding a New Table or Column

1. Update or add a SQLAlchemy model in `app/models/`.
2. Run:
    ```sh
    alembic revision --autogenerate -m "describe change"
    alembic upgrade head
    ```

---

## 7. Troubleshooting

- If Alembic can't connect, check your environment variables.
- If you see `Unknown database`, create the DB manually.
- For more info, see [Alembic documentation](https://alembic.sqlalchemy.org/en/latest/).

---

## 8. Docker Compose Integration

If running via Docker Compose, set the environment variables in `docker-compose.yml` under the `api` service.

---

## 9. References

- [alembic.ini](../alembic.ini)
- [migrations/env.py](../migrations/env.py)
- [app/models/](../app/models/)

---

For questions, contact the backend team.

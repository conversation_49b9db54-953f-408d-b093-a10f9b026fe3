"""added post processing_status to calls

Revision ID: f323fb7fdf62
Revises: c8fc3aa732d6
Create Date: 2025-05-21 12:14:50.383975

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f323fb7fdf62'
down_revision: Union[str, None] = 'c8fc3aa732d6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('calls', sa.Column('post_processing_status', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('calls', 'post_processing_status')
    # ### end Alembic commands ###

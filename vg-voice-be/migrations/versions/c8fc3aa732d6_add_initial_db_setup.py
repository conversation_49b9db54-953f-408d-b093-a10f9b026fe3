"""add initial db setup

Revision ID: c8fc3aa732d6
Revises: 
Create Date: 2025-05-20 21:45:10.613826

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c8fc3aa732d6'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('password', sa.String(length=255), nullable=True),
    sa.Column('first_name', sa.String(length=255), nullable=True),
    sa.Column('last_name', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_table('call_batches',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=200), nullable=True),
    sa.Column('file_url', sa.String(length=200), nullable=True),
    sa.Column('status', sa.String(length=200), nullable=True),
    sa.Column('failure_reason', sa.String(length=200), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('projects',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('language', sa.String(length=50), nullable=False),
    sa.Column('region', sa.String(length=100), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('personas',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.BigInteger(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('updated_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('project_id', 'name', name='uix_persona_project_name')
    )
    op.create_table('questionnaires',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.BigInteger(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('updated_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('persona_versions',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('persona_id', sa.BigInteger(), nullable=False),
    sa.Column('voice_type', sa.String(length=100), nullable=False),
    sa.Column('engine', sa.String(length=100), nullable=False),
    sa.Column('language', sa.String(length=50), nullable=False),
    sa.Column('from_number', sa.String(length=50), nullable=False),
    sa.Column('version', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('change_notes', sa.Text(), nullable=True),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['persona_id'], ['personas.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('prompts',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('persona_id', sa.BigInteger(), nullable=False),
    sa.Column('type', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('updated_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['persona_id'], ['personas.id'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('persona_id', 'type', name='uix_prompt_persona_type')
    )
    op.create_table('questionnaire_versions',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('question_text', sa.Text(), nullable=False),
    sa.Column('question_type', sa.String(length=50), nullable=False),
    sa.Column('options', sa.Text(), nullable=True),
    sa.Column('order_no', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('version', sa.String(length=50), nullable=False),
    sa.Column('change_notes', sa.Text(), nullable=True),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('prompt_versions',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('prompt_id', sa.BigInteger(), nullable=False),
    sa.Column('prompt_text', sa.Text(), nullable=True),
    sa.Column('version', sa.String(length=50), nullable=False),
    sa.Column('model', sa.String(length=100), nullable=True),
    sa.Column('change_notes', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['prompt_id'], ['prompts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('calls',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.BigInteger(), nullable=False),
    sa.Column('persona_version_id', sa.BigInteger(), nullable=False),
    sa.Column('conversational_prompt_version_id', sa.BigInteger(), nullable=False),
    sa.Column('call_batch_id', sa.BigInteger(), nullable=True),
    sa.Column('call_type', sa.String(length=20), nullable=True),
    sa.Column('to_number', sa.String(length=20), nullable=True),
    sa.Column('from_number', sa.String(length=20), nullable=True),
    sa.Column('variables', sa.JSON(none_as_null=100), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('failure_reason', sa.String(length=100), nullable=True),
    sa.Column('request_id', sa.String(length=200), nullable=True),
    sa.Column('audio_url', sa.String(length=500), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('ended_by', sa.String(length=20), nullable=True),
    sa.Column('created_by_id', sa.BigInteger(), nullable=True),
    sa.Column('updated_by_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), nullable=True),
    sa.ForeignKeyConstraint(['call_batch_id'], ['call_batches.id'], ),
    sa.ForeignKeyConstraint(['conversational_prompt_version_id'], ['prompt_versions.id'], ),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['persona_version_id'], ['persona_versions.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ),
    sa.ForeignKeyConstraint(['updated_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('request_id')
    )
    op.create_table('call_responses',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('call_id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_version_id', sa.BigInteger(), nullable=False),
    sa.Column('answer_text', sa.Text(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
    sa.ForeignKeyConstraint(['call_id'], ['calls.id'], ),
    sa.ForeignKeyConstraint(['questionnaire_version_id'], ['questionnaire_versions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('call_responses')
    op.drop_table('calls')
    op.drop_table('prompt_versions')
    op.drop_table('questionnaire_versions')
    op.drop_table('prompts')
    op.drop_table('persona_versions')
    op.drop_table('questionnaires')
    op.drop_table('personas')
    op.drop_table('projects')
    op.drop_table('call_batches')
    op.drop_table('users')
    # ### end Alembic commands ###

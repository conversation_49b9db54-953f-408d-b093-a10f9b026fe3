This is a Python fastapi application for the VEGROW Voice BE.


## Tech Stack

Keep on updating docs as per the changes in the code.
Docs are basically for reference of developers on how to make changes in the code and give them information about business flows
maintain requirements.txt file with proper version and dependencies
Always use env config from the centralized envconfig manager
Always add constants to a centralized constants file inside config folder
Never use enums in DB models

📘 API Design Rules

Modular Routing: Utilize APIRouter to organize routes by functionality or version (e.g., api/v1/users.py).

Data Validation: Employ Pydantic models for all request and response bodies to ensure data integrity.

Consistent Status Codes: Return appropriate HTTP status codes (e.g., 200 OK, 201 Created, 400 Bad Request).

Error Handling: Implement custom exception handlers to manage errors gracefully and provide meaningful responses.

API Documentation: Leverage FastAPIs automatic documentation generation and add descriptive tags and summaries to endpoints.

🗂️ Project Structure Guidelines
Organized Directory Layout:

app/
├── main.py
├── api/
│   └── v1/
│       ├── endpoints/
│       └── dependencies/
├── core/
├── models/
├── schemas/
├── services/
├── migrations/
├── db/
├── config/
├── utils/
└── docs/
    └── PRDs/


Separation of Concerns:

api/: Route definitions and dependencies.
core/: Configuration and application settings.

models/: Database models.

schemas/: Pydantic models for data validation.

services/: Business logic and service layer.

db/: Database session and initialization.

utils/: Utility functions and helpers.

🧑‍💻 Coding Standards
PEP8 Compliance: Adhere to PEP8 guidelines for code style and formatting.

Type Annotations: Use Python type hints throughout your codebase for clarity and better tooling support.

Asynchronous Programming: Define route handlers with async def to take advantage of FastAPIs asynchronous capabilities.

Dependency Injection: Utilize FastAPIs Depends for managing dependencies and promoting modularity.

Configuration Management: Use Pydantics BaseSettings for managing application settings and environment variables.




from typing import Dict, Any, Optional, Callable, Tuple
import time
from app.utils.logger import get_logger

logger = get_logger(__name__)

class SimpleCache:
    """
    A simple in-memory cache with TTL support.
    """
    
    def __init__(self, ttl_seconds: int = 300):
        """Initialize cache with TTL in seconds."""
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.ttl_seconds = ttl_seconds
    
    def get(self, key: str) -> Optional[Any]:
        """Get a value from cache if it exists and is not expired."""
        if key not in self.cache:
            return None
            
        value, timestamp = self.cache[key]
        if time.time() - timestamp > self.ttl_seconds:
            # Value has expired
            del self.cache[key]
            return None
            
        return value
        
    def set(self, key: str, value: Any) -> None:
        """Store a value in the cache with current timestamp."""
        self.cache[key] = (value, time.time())
        
    def invalidate(self, key: str) -> None:
        """Remove a specific key from the cache."""
        if key in self.cache:
            del self.cache[key]
            
    def invalidate_pattern(self, pattern: str) -> None:
        """Remove all keys that contain the pattern."""
        keys_to_delete = [k for k in self.cache.keys() if pattern in k]
        for key in keys_to_delete:
            del self.cache[key]
            
    def clear(self) -> None:
        """Clear the entire cache."""
        self.cache.clear()
        
    def get_or_set(self, key: str, value_func: Callable[[], Any]) -> Any:
        """
        Get value from cache if it exists, otherwise call the function,
        store result in cache and return it.
        """
        value = self.get(key)
        if value is not None:
            logger.debug(f"Cache hit for key: {key}")
            return value
            
        logger.debug(f"Cache miss for key: {key}")
        value = value_func()
        self.set(key, value)
        return value


# Create a singleton instance
cache = SimpleCache()

# Helper decorator for caching function results
def cached(key_prefix: str, ttl_seconds: Optional[int] = None):
    """
    Decorator to cache function results.
    
    Args:
        key_prefix: Prefix for the cache key
        ttl_seconds: Optional custom TTL. If None, uses the default TTL.
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Create a cache key from the function name, args and kwargs
            key_parts = [key_prefix, func.__name__]
            
            # Add args to key
            if args:
                for arg in args:
                    # Skip session objects for database functions
                    if str(type(arg)).lower().find("session") != -1:
                        continue
                    key_parts.append(str(arg))
                    
            # Add sorted kwargs to key for consistency
            if kwargs:
                for k in sorted(kwargs.keys()):
                    v = kwargs[k]
                    # Skip session objects for database functions
                    if str(type(v)).lower().find("session") != -1:
                        continue
                    key_parts.append(f"{k}:{v}")
                    
            cache_key = ":".join(key_parts)
            
            # Use custom cache if ttl is provided
            if ttl_seconds is not None:
                temp_cache = SimpleCache(ttl_seconds=ttl_seconds)
                return temp_cache.get_or_set(cache_key, lambda: func(*args, **kwargs))
            else:
                return cache.get_or_set(cache_key, lambda: func(*args, **kwargs))
                
        return wrapper
    return decorator
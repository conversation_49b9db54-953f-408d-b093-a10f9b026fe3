import logging
import sys
import contextvars
from app.config.envconfig import settings

# Context variable to store trace ID per request
trace_id_ctx_var = contextvars.ContextVar("trace_id", default=None)

def set_trace_id(trace_id: str):
    trace_id_ctx_var.set(trace_id)

def get_trace_id() -> str:
    return trace_id_ctx_var.get() or "-"

class TraceIdFilter(logging.Filter):
    def filter(self, record):
        try:
            record.trace_id = get_trace_id() or "-"
        except Exception:
            record.trace_id = "-"
        return True

LOG_FORMAT = (
    "%(asctime)s | %(levelname)s | %(name)s | %(process)d | %(thread)d | "
    "%(filename)s:%(lineno)d | [trace_id=%(trace_id)s] | %(message)s"
)

LOG_LEVEL = logging.INFO

class SafeFormatter(logging.Formatter):
    def format(self, record):
        if not hasattr(record, "trace_id"):
            record.trace_id = "-"
        return super().format(record)

handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(SafeFormatter(LOG_FORMAT))

_root_logger = logging.getLogger()
_root_logger.setLevel(LOG_LEVEL)
if not _root_logger.hasHandlers():
    _root_logger.addHandler(handler)
for h in _root_logger.handlers:
    h.addFilter(TraceIdFilter())

# Utility to get a logger for a module/class

def get_logger(name: str = None):
    logger = logging.getLogger(name)
    logger.addFilter(TraceIdFilter())
    return logger

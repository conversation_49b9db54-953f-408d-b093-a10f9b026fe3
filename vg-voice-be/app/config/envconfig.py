from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    environment: str = Field("development", env="ENV")
    
    # AWS S3 Configuration
    aws_access_key_id: str = Field(..., env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: str = Field(..., env="AWS_SECRET_ACCESS_KEY")
    aws_s3_region: str = Field("ap-south-1", env="AWS_S3_REGION")
    aws_s3_bucket: str = Field(..., env="AWS_S3_BUCKET")
    
    # MySQL Configuration
    mysql_host: str = Field(..., env="MYSQL_HOST")
    mysql_port: int = Field(3306, env="MYSQL_PORT")
    mysql_user: str = Field(..., env="MYSQL_USER")
    mysql_password: str = Field(..., env="MYSQL_PASSWORD")
    mysql_database: str = Field(..., env="MYSQL_DATABASE")

    # MongoDB Configuration
    mongo_uri: str = Field(..., env="MONGO_URI")
    mongo_db: str = Field(..., env="MONGO_DB")

    # External Services
    velynk_service_host: str = Field("http://localhost:3000", env="VELYNK_SERVICE_HOST")
    third_party_api_key: str = Field("f91ac9b7dcef465c90ea529001821e12", env="THIRD_PARTY_API_KEY")
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    vg_voice_be_host: str = Field("http://localhost:7004", env="VG_VOICE_BE_HOST")
    vg_voice_be_host_web_socket: str = Field("ws://localhost:7004", env="VG_VOICE_BE_HOST_WEB_SOCKET")
    llm_service_host: str = Field("http://localhost:8000", env="LLM_SERVICE_HOST")
    llm_service_api_key: str = Field(..., env="LLM_SERVICE_API_KEY")
    
    # Exotel Configuration
    exotel_account_sid: str = Field(..., env="EXOTEL_ACCOUNT_SID")
    exotel_api_key: str = Field(..., env="EXOTEL_API_KEY")
    exotel_api_token: str = Field(..., env="EXOTEL_API_TOKEN")
    exotel_subdomain: str = Field("", env="EXOTEL_SUBDOMAIN")
    
    # constants
    from_number_flow_id_map: dict[str, str] = {
        "***********": "961832",
    }
    auth_jwt_secret: str = "e03fc380861cd27b9e97621762e3ca4977627fadc2ec8e14cc4f256b9d536ddeba301fce9a6dced1e73e77636c1e66238f214a317e23cf075b4ba8173d7954bd"
    auth_jwt_algorithm: str = "HS256"
    auth_jwt_expire_minutes: int = 60 * 24 * 30

settings = Settings()
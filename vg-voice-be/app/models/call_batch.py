from sqlalchemy import Column, BigInteger, String, TIMESTAMP, Integer, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from app.models.project import Base

class CallBatch(Base):
    __tablename__ = "call_batches"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=True)
    file_url = Column(String(200), nullable=True)
    status = Column(String(200), nullable=True)
    failure_reason = Column(String(200), nullable=True)
    retry_count = Column(Integer, nullable=True)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=True)

from sqlalchemy import Column, <PERSON>I<PERSON><PERSON>, String, T<PERSON><PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>, ForeignKey, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import enum

from app.models.project import Base
from app.models.call_batch import CallBatch

class CallType(str, enum.Enum):
    OUTBOUND = "outbound"
    INBOUND = "inbound"

class CallStatus(str, enum.Enum):
    PENDING = "pending"
    IN_QUEUE = "in-queue"
    SYSTEM_FAILURE = "system-failure"
    INITIATED = "initiated"
    RINGING = "ringing"
    BUSY = "busy"
    COMPLETED = "completed"
    FAILED = "failed"
    IN_PROGRESS = "in-progress"
    NO_ANSWER = "no-answer"
    
    ALLOW_RETRY = [FAILED, SYSTEM_FAILURE, NO_ANSWER, BUSY]

class PostProcessingStatus(str, enum.Enum):
    PENDING = "pending"
    IN_PROGRESS = "in-progress"
    COMPLETED = "completed"
    FAILED = "failed"

class Call(Base):
    __tablename__ = "calls"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    project_id = Column(BigInteger, ForeignKey("projects.id"), nullable=False)
    persona_version_id = Column(BigInteger, ForeignKey("persona_versions.id"), nullable=False)
    conversational_prompt_version_id = Column(BigInteger, ForeignKey("prompt_versions.id"), nullable=False)
    call_batch_id = Column(BigInteger, ForeignKey("call_batches.id"), nullable=True)
    call_type = Column(String(20), nullable=True)
    to_number = Column(String(20), nullable=True)
    from_number = Column(String(20), nullable=True)
    variables = Column(JSON(100), nullable=True)
    status = Column(String(20), nullable=True)
    post_processing_status = Column(String(20), default=PostProcessingStatus.PENDING)
    failure_reason = Column(String(100), nullable=True)
    request_id = Column(String(200), unique=True, nullable=True)
    audio_url = Column(String(500), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    ended_by = Column(String(20), nullable=True)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=True)

    prompt_version = relationship("PromptVersion", back_populates="calls")
    persona_version = relationship("PersonaVersion", back_populates="calls")
    call_responses = relationship("CallResponse", back_populates="call")
from sqlalchemy import Column, BigInteger, Text, TIMESTAMP, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

from app.models.project import Base

class CallResponse(Base):
    __tablename__ = "call_responses"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    call_id = Column(BigInteger, ForeignKey("calls.id"), nullable=False)
    questionnaire_version_id = Column(BigInteger, ForeignKey("questionnaire_versions.id"), nullable=False)
    answer_text = Column(Text, nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    
    questionnaire_version = relationship("QuestionnaireVersion", back_populates="call_responses")
    call = relationship("Call", back_populates="call_responses")

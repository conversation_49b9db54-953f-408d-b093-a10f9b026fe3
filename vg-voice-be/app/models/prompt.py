from sqlalchemy import Column, BigInteger, String, TIMESTAMP, Text, ForeignKey, Boolean, UniqueConstraint
from sqlalchemy.orm import relationship
import enum

from app.models.project import Base

SUMMARY_PROMPT_QUESTIONS_KEY = "questionnaire_json"

class PromptModel(str, enum.Enum):
    GPT_4 = 'gpt-4'
    GPT_3_5 = 'gpt-3.5-turbo'
    CLAUDE_2 = 'claude-2'
    LLAMA_2 = 'llama-2'
    GEMINI_PRO = 'gemini-pro'

class PromptType(str, enum.Enum):
    CONVERSATIONAL = "Conversational"
    SUMMARY = "Summary"
    FALLBACK = "Fallback"
    INSIGHTS = "Insights"
    
class PromptStatus(str, enum.Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    ARCHIVED = "Archived"
    TESTING = "Testing"
    INACTIVE = "Inactive"

class Prompt(Base):
    __tablename__ = "prompts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    persona_id = Column(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("personas.id"), nullable=False)
    type = Column(String(20), nullable=False)
    status = Column(String(50), nullable=False, default=PromptStatus.ACTIVE)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    # Define relationship to versions
    versions = relationship("PromptVersion", back_populates="prompt", cascade="all, delete-orphan")
    
    # Add unique constraint on persona_id and type
    __table_args__ = (
        UniqueConstraint('persona_id', 'type', name='uix_prompt_persona_type'),
    )

class PromptVersion(Base):
    __tablename__ = "prompt_versions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    prompt_id = Column(BigInteger, ForeignKey("prompts.id"), nullable=False)
    prompt_text = Column(Text, nullable=True)
    version = Column(String(50), nullable=False)
    model = Column(String(100), nullable=True)
    change_notes = Column(Text, nullable=True)
    status = Column(String(50), nullable=False, default=PromptStatus.DRAFT)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    # Define relationship to parent prompt
    prompt = relationship("Prompt", back_populates="versions")
    calls = relationship("Call", back_populates="prompt_version")


from sqlalchemy import Column, BigInteger, String, Enum, TIMESTAMP, <PERSON><PERSON><PERSON>, Integer, Text
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
import enum

from app.models.project import Base

class QuestionnaireStatus(str, enum.Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    ARCHIVED = "Archived"
    TESTING = "Testing"
    INACTIVE = "Inactive"

class QuestionType(str, enum.Enum):
    TEXT = "Text"
    NUMBER = "Number"
    DROPDOWN = "Dropdown"
    RADIO = "Radio"
    CHECKBOX = "Checkbox"
    DATE = "Date"

class Questionnaire(Base):
    __tablename__ = "questionnaires"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    project_id = Column(BigInteger, ForeignKey("projects.id"), nullable=False)
    status = Column(String(50), nullable=False, default=QuestionnaireStatus.ACTIVE)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(BigInteger, Foreign<PERSON>ey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    # Define relationship to versions
    versions = relationship("QuestionnaireVersion", back_populates="questionnaire", cascade="all, delete-orphan")

class QuestionnaireVersion(Base):
    __tablename__ = "questionnaire_versions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id"), nullable=False)
    question_text = Column(Text, nullable=False)
    question_type = Column(String(50), nullable=False, default=QuestionType.TEXT)
    options = Column(Text, nullable=True)  # separator " || " separated options
    order_no = Column(Integer, nullable=False)
    status = Column(String(50), nullable=False, default=QuestionnaireStatus.DRAFT)
    version = Column(String(50), nullable=False)
    change_notes = Column(Text, nullable=True)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    questionnaire = relationship("Questionnaire", back_populates="versions")
    call_responses = relationship("CallResponse", back_populates="questionnaire_version")

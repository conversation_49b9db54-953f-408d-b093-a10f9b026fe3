from typing import Optional
from sqlalchemy import <PERSON>umn, BigInteger, String, TIMESTAMP, ForeignKey, Text, Boolean, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from pipecat.transcriptions.language import Language
import enum

from app.models.project import Base

class CustomAIVoiceType(str, enum.Enum):
    PUCK = "Puck"
    CHARON = "Charon"
    KORE = "Kore"
    FENRIR = "Fenrir"
    AODE = "Aoede"
    LEDA = "Leda"
    ORUS = "Orus"
    ZEPHYR = "Zephyr"
    
class PersonaStatus(str, enum.Enum):
    DRAFT = "Draft"
    ACTIVE = "Active"
    ARCHIVED = "Archived"
    TESTING = "Testing"
    INACTIVE = "Inactive"
    
class PersonaEngine(str, enum.Enum):
    CUSTOM = "Custom"
    BLAND = "Bland"
    
class PersonaLanguage(str, enum.Enum):
    ENGLISH = "English"
    HINDI = "Hindi"
    TAMIL = "Tamil"
    TELUGU = "Telugu"
    MALAYALAM = "Malayalam"
    KANNADA = "Kannada"
    BENGALI = "Bengali"
    GUJARATI = "Gujarati"
    MARATHI = "Marathi"
    
    @staticmethod
    def get_language_code(lamguage: str) -> Optional[str]:
        """Get the language code for a given language name."""
        language_codes = {
            PersonaLanguage.ENGLISH: Language.EN,
            PersonaLanguage.HINDI: Language.HI,
            PersonaLanguage.TAMIL: Language.TA,
            PersonaLanguage.TELUGU: Language.TE,
            PersonaLanguage.MALAYALAM: Language.ML,
            PersonaLanguage.KANNADA: Language.KN,
            PersonaLanguage.BENGALI: Language.BN,
            PersonaLanguage.GUJARATI: Language.GU,
            PersonaLanguage.MARATHI: Language.MR
        }
        return language_codes.get(lamguage, None)

class Persona(Base):
    __tablename__ = "personas"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    project_id = Column(BigInteger, ForeignKey("projects.id"), nullable=False)
    name = Column(String(100), nullable=False)
    is_default = Column(Boolean, nullable=False, default=False)
    status = Column(String(50), nullable=False, default=PersonaStatus.ACTIVE)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    updated_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    # Define relationship to versions
    versions = relationship("PersonaVersion", back_populates="persona", cascade="all, delete-orphan")

    # Add unique constraint on persona_id and type
    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='uix_persona_project_name'),
    )
    
class PersonaVersion(Base):
    __tablename__ = "persona_versions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    persona_id = Column(BigInteger, ForeignKey("personas.id"), nullable=False)
    voice_type = Column(String(100), nullable=False)
    engine = Column(String(100), nullable=False)
    language = Column(String(50), nullable=False)
    from_number = Column(String(50), nullable=False)
    version = Column(String(50), nullable=False)
    status = Column(String(50), nullable=False, default=PersonaStatus.DRAFT)
    change_notes = Column(Text, nullable=True)
    created_by_id = Column(BigInteger, ForeignKey("users.id"), nullable=True)
    created_at = Column(TIMESTAMP, nullable=False)
    updated_at = Column(TIMESTAMP, nullable=False)
    
    # Define relationship to parent persona
    persona = relationship("Persona", back_populates="versions")
    calls = relationship("Call", back_populates="persona_version")

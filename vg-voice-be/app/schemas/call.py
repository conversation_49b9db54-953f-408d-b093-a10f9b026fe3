from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from app.models.call import CallStatus, CallType


class CallTriggerRequest(BaseModel):
    project_id: int
    persona_version_id: Optional[int] = None
    phone_number: str
    call_type: CallType = CallType.OUTBOUND
    variables: Optional[Dict[str, Any]] = None

class CallTriggerCustomRequest(BaseModel):
    project_id: int
    persona_version_id: int
    phone_numbers: list[str]
    call_type: CallType = CallType.OUTBOUND
    variables: Optional[Dict[str, Any]] = None

class CallResponseRequest(BaseModel):
    answers: dict

class CallDetails(BaseModel):
    id: int
    to_number: str
    status: CallStatus = CallStatus.PENDING
    duration_seconds: Optional[float]
    created_at: datetime
    audio_url: Optional[str]
    failure_reason: Optional[str]
    persona_name: Optional[str] = None

class CallBatchDetails(BaseModel):
    id: int
    name: Optional[str]
    status: CallStatus = CallStatus.PENDING
    created_at: datetime
    total_calls: int
    completed_calls: int
    failed_calls: int
    in_progress_calls: int

class CallListResponseMetadata(BaseModel):
    total: int
    skip: int
    limit: int
    has_more: bool
    filters_applied: Optional[Dict[str, Any]] = None

class CallListResponse(BaseModel):
    metadata: CallListResponseMetadata
    items: List[CallDetails]

class CallStatistics(BaseModel):
    total_calls: int
    completed_calls: int
    avg_call_duration: float
    success_rate: float

class CallResponseUpdateRequest(BaseModel):
    response_id: int
    answer_text: str

class CallResponseUpdateBulkRequest(BaseModel):
    updates: List[CallResponseUpdateRequest]

class CallResponseItemUpdate(BaseModel):
    response_id: int
    answer_text: str

class CallResponsesUpdateRequest(BaseModel):
    updates: List[CallResponseItemUpdate]
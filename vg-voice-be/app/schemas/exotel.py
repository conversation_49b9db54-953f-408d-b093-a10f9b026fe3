
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from app.models.persona import CustomAIVoiceType, PersonaStatus, PersonaEngine

# PersonaVersion schemas
class ExotelInitiateCallResponse(BaseModel):
    url : str = Field(
        ...,
        description="WebSocket URL for audio streaming",
        example="wss://example.com/ws/exotel/12345/your_api_key"
    )
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class ProjectBase(BaseModel):
    title: str
    language: str
    region: Optional[str] = None

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    title: Optional[str] = None
    language: Optional[str] = None
    region: Optional[str] = None
    status: Optional[str] = None

class ProjectOut(ProjectBase):
    id: int
    status: str
    created_at: datetime
    updated_at: datetime
    created_by_id: Optional[int] = None

    class Config:
        from_attributes = True

class ProjectListResponseMetadata(BaseModel):
    total: int
    skip: int
    limit: int
    has_more: bool
    filters_applied: Optional[Dict[str, Any]] = None

class ProjectListResponse(BaseModel):
    metadata: ProjectListResponseMetadata
    items: List[ProjectOut]

class ProjectStatsResponse(BaseModel):
    total: int
    by_status: Dict[str, int]

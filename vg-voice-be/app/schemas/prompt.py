from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from app.models.prompt import PromptType, PromptStatus, PromptModel

# PromptVersion schemas
class PromptVersionBase(BaseModel):
    prompt_text: str
    model: PromptModel

class PromptVersionCreate(PromptVersionBase):
    change_notes: Optional[str] = None
    status: PromptStatus = PromptStatus.DRAFT

class PromptVersionUpdate(BaseModel):
    prompt_text: Optional[str] = None
    model: Optional[PromptModel] = None
    status: Optional[PromptStatus] = None
    change_notes: Optional[str] = None

class PromptVersionOut(PromptVersionBase):
    id: int
    prompt_id: int
    version: str
    status: PromptStatus
    change_notes: Optional[str] = None
    created_by_id: Optional[int] = None
    variables: Optional[List[str]] = []
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Prompt schemas
class PromptBase(BaseModel):
    persona_id: int
    type: PromptType

class PromptCreate(PromptBase):
    # Initial version data is provided when creating a prompt
    prompt_text: str
    status: PromptStatus = PromptStatus.ACTIVE
    model: PromptModel

class PromptUpdate(BaseModel):
    type: Optional[PromptType] = None
    status: Optional[PromptStatus] = None

class PromptOut(PromptBase):
    id: int
    status: PromptStatus
    created_by_id: Optional[int] = None
    updated_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    # Current active version (if any)
    current_version: Optional[PromptVersionOut] = None
    

    class Config:
        from_attributes = True

class PromptWithVersionsOut(PromptOut):
    versions: List[PromptVersionOut] = []

class PromptListResponseMetadata(BaseModel):
    total: int
    skip: int
    limit: int
    has_more: bool
    filters_applied: Optional[Dict[str, Any]] = None

class PromptListResponse(BaseModel):
    metadata: PromptListResponseMetadata
    items: List[PromptOut]

class PromptStatsResponse(BaseModel):
    total: int
    by_status: Dict[str, int]
    by_persona: Dict[str, int]
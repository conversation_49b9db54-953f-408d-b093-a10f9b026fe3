from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, Dict, Any


class UserBase(BaseModel):
    username: str
    email: str


class UserCreate(UserBase):
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class UserLogin(BaseModel):
    email: str
    password: str


class GoogleAuthInput(BaseModel):
    id_token: str


class UserOut(UserBase):
    id: int
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool

    class Config:
        orm_mode = True


class UserProfile(UserOut):
    pass


class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserOut
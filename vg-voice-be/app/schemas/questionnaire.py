from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from app.models.questionnaire import QuestionnaireStatus, QuestionType

# QuestionnaireVersion schemas
class QuestionnaireVersionBase(BaseModel):
    question_text: str
    question_type: QuestionType
    options: Optional[str] = None
    order_no: int

class QuestionnaireVersionCreate(QuestionnaireVersionBase):
    change_notes: Optional[str] = None
    status: QuestionnaireStatus = QuestionnaireStatus.DRAFT

class QuestionnaireVersionUpdate(BaseModel):
    question_text: Optional[str] = None
    question_type: Optional[QuestionType] = None
    options: Optional[str] = None
    order_no: Optional[int] = None
    status: Optional[QuestionnaireStatus] = None
    change_notes: Optional[str] = None

class QuestionnaireVersionOut(QuestionnaireVersionBase):
    id: int
    questionnaire_id: int
    version: str
    status: QuestionnaireStatus
    change_notes: Optional[str] = None
    created_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Questionnaire schemas
class QuestionnaireBase(BaseModel):
    project_id: int

class QuestionnaireCreate(QuestionnaireBase):
    # Initial version data is provided when creating a questionnaire
    question_text: str
    question_type: QuestionType = QuestionType.TEXT
    options: Optional[str] = None
    status: QuestionnaireStatus = QuestionnaireStatus.ACTIVE
    order_no: int

class QuestionnaireUpdate(BaseModel):
    status: Optional[QuestionnaireStatus] = None

class QuestionnaireOut(QuestionnaireBase):
    id: int
    status: QuestionnaireStatus
    created_by_id: Optional[int] = None
    updated_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    # Current active version (if any)
    current_version: Optional[QuestionnaireVersionOut] = None

    class Config:
        from_attributes = True

class QuestionnaireWithVersionsOut(QuestionnaireOut):
    versions: List[QuestionnaireVersionOut] = []

class QuestionnaireListResponseMetadata(BaseModel):
    total: int
    skip: int
    limit: int
    has_more: bool
    filters_applied: Optional[Dict[str, Any]] = None

class QuestionnaireListResponse(BaseModel):
    metadata: QuestionnaireListResponseMetadata
    items: List[QuestionnaireOut]

class QuestionnaireStatsResponse(BaseModel):
    total: int
    by_status: Dict[str, int]
    by_project: Dict[str, int]
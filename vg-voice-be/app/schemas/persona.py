from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from app.models.persona import CustomAIVoiceType, PersonaStatus, PersonaEngine

# PersonaVersion schemas
class PersonaVersionBase(BaseModel):
    voice_type: CustomAIVoiceType
    engine: PersonaEngine
    language: str
    from_number: str

class PersonaVersionCreate(PersonaVersionBase):
    change_notes: Optional[str] = None
    status: PersonaStatus = PersonaStatus.DRAFT

class PersonaVersionUpdate(BaseModel):
    voice_type: Optional[CustomAIVoiceType] = None
    engine: Optional[PersonaEngine] = None
    language: Optional[str] = None
    from_number: Optional[str] = None
    status: Optional[PersonaStatus] = None
    change_notes: Optional[str] = None

class PersonaVersionOut(PersonaVersionBase):
    id: int
    persona_id: int
    version: str
    status: PersonaStatus
    change_notes: Optional[str] = None
    created_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Persona schemas
class PersonaBase(BaseModel):
    project_id: int
    name: str
    is_default: bool = False

class PersonaCreate(PersonaBase):
    # Initial version data is provided when creating a persona
    voice_type: CustomAIVoiceType
    status: PersonaStatus = PersonaStatus.ACTIVE
    engine: PersonaEngine
    language: str
    from_number: str

class PersonaUpdate(BaseModel):
    name: Optional[str] = None
    is_default: Optional[bool] = None
    status: Optional[PersonaStatus] = None

class PersonaOut(PersonaBase):
    id: int
    status: str
    created_by_id: Optional[int] = None
    updated_by_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    # Current active version (if any)
    current_version: Optional[PersonaVersionOut] = None

    class Config:
        from_attributes = True

class PersonaWithVersionsOut(PersonaOut):
    versions: List[PersonaVersionOut] = []

class PersonaListResponseMetadata(BaseModel):
    total: int
    skip: int
    limit: int
    has_more: bool
    filters_applied: Optional[Dict[str, Any]] = None

class PersonaListResponse(BaseModel):
    metadata: PersonaListResponseMetadata
    items: List[PersonaOut]

class PersonaStatsResponse(BaseModel):
    total: int
    by_status: Dict[str, int]
    by_project: Dict[str, int]
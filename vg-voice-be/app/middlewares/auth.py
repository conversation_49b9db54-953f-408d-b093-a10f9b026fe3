import fnmatch

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from app.services.velynk_service import get_current_user_profile
from app.services.user_service import validate_token
from app.config.envconfig import settings
from app.utils.logger import get_logger
from app.db import SessionLocal

logger = get_logger(__name__)

SKIP_AUTH_ENDPOINTS = [
    {"path": "/ping", "method": "GET"},
    {"path": "/voice/api/openapi.json", "method": "GET"},
    {"path": "/voice/docs", "method": "GET"},
    {"path": "/voice/redoc", "method": "GET"},
    {"path": "/voice/api/v1/files/*", "method": "GET"},
    {"path": "/voice/api/v1/users/google_authorization", "method": "POST"},
]

class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.method == "OPTIONS" or request.method == "HEAD":
            return await call_next(request)
        
        for rule in SKIP_AUTH_ENDPOINTS:
            if fnmatch.fnmatch(request.url.path, rule["path"]) and request.method.upper() == rule["method"]:
                return await call_next(request)
        
        api_key_auth_endpoints = ["/voice/api/v1/exotel/status_callback", "/voice/api/v1/exotel/get_call_ws_url", "/voice/api/v1/calls/retrigger_post_call_processing"]
        if request.url.path in api_key_auth_endpoints:
            api_key = request.query_params.get("api_key")
            if api_key == settings.third_party_api_key:
                return await call_next(request)
            else:
                return JSONResponse(status_code=401, content={"detail": "Invalid API key"})

        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return JSONResponse(status_code=401, content={"detail": "Missing Authorization header"})

        db = SessionLocal()
        try:
            token = auth_header.split(" ")[1]
            user_info = validate_token(db, token)
            request.state.user = user_info
            

        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
            return JSONResponse(status_code=401, content={"detail": "Invalid or expired token"})
        finally:
            db.close()

        return await call_next(request)

import time
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from app.utils.logger import get_logger
import uuid
from app.utils.logger import set_trace_id

logger = get_logger("request_logger")

class LoggerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.method == "OPTIONS":
            return await call_next(request)
        
        start_time = time.time()
        trace_id = request.headers.get("x-trace-id") or str(uuid.uuid4())
        set_trace_id(trace_id)
        request.state.trace_id = trace_id
        
        body = await request.body()
        logger.info(f"Incoming request: {request.method} {request.url.path} | Query: {dict(request.query_params)} | Body: {body.decode('utf-8', 'ignore')}")
        
        response: Response = await call_next(request)
        
        process_time = (time.time() - start_time) * 1000  # ms
        logger.info(f"Response {request.method} {request.url.path} | Status: {response.status_code} | Time: {process_time:.2f}ms")
        
        response.headers["x-trace-id"] = trace_id
        return response

from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.schemas.project import (
    ProjectCreate, 
    ProjectUpdate, 
    ProjectOut, 
    ProjectListResponse,
    ProjectListResponseMetadata,
    ProjectStatsResponse
)
from app.models.project import ProjectStatus
from app.services.project_service import (
    get_projects, 
    get_project, 
    create_project, 
    update_project, 
    delete_project,
    get_project_stats
)
from app.db import get_db

router = APIRouter()

@router.get(
    "/projects", 
    response_model=ProjectListResponse,
    summary="Get all projects with filtering and search options",
    tags=["projects"],
    description="""
    Get a list of all projects with pagination, filtering, and search options.
    
    Projects are the top-level containers for persona, prompts, and calls. Each project 
    represents a specific calling campaign with its own settings and configurations.
    
    The response includes pagination metadata and a list of project details.
    """
)
def list_projects(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term to filter projects by title, language or region"),
    status: Optional[str] = Query(None, description="Filter projects by status"),
    language: Optional[str] = Query(None, description="Filter projects by language"),
    region: Optional[str] = Query(None, description="Filter projects by region"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get list of projects with filtering, search and pagination options.
    """
    # Build filters dict from query parameters
    filters = {}
    if status:
        filters["status"] = status
    if language:
        filters["language"] = language
    if region:
        filters["region"] = region
        
    projects, total = get_projects(
        db=db, 
        skip=skip, 
        limit=limit,
        filters=filters,
        search=search,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    # Convert to response models
    items_out = [ProjectOut.model_validate(item) for item in projects]
    
    metadata = ProjectListResponseMetadata(
        total=total,
        skip=skip,
        limit=limit,
        has_more=total > (skip + limit),
        filters_applied=filters if filters else None
    )
    
    return ProjectListResponse(
        metadata=metadata,
        items=items_out
    )

@router.get(
    "/projects/statistics", 
    response_model=ProjectStatsResponse,
    summary="Get project statistics",
    tags=["statistics"],
    description="""
    Get aggregated statistics about projects, grouped by various dimensions.
    
    The response includes total project counts and breakdowns by:
    - Status (Active, Draft, Archived, etc.)
    - Language
    - Region
    """
)
def get_projects_stats(db: Session = Depends(get_db)):
    """
    Get statistics about projects, grouped by status, language, etc.
    """
    return get_project_stats(db)

@router.get(
    "/projects/{project_id}", 
    response_model=ProjectOut,
    summary="Get a specific project by ID",
    tags=["projects"],
    description="""
    Get detailed information about a specific project by its ID.
    
    This endpoint returns all the project details including its configuration 
    settings, status, and associated metadata.
    """
)
def read_project(
    project_id: int = Path(..., ge=1, description="The ID of the project to get"),
    db: Session = Depends(get_db)
):
    """
    Get a project by its ID.
    """
    project = get_project(db, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.post(
    "/projects", 
    response_model=ProjectOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new project",
    tags=["projects"],
    description="""
    Create a new project with the specified configuration.
    
    Projects are the top-level entities in the system. Each project represents a specific
    calling campaign with its configuration and settings. Projects can have multiple personas,
    prompts, and call sessions associated with them.
    
    The creator's ID will be automatically captured if available in the request state.
    """
)
def create_new_project(
    project_in: ProjectCreate, 
    request: Request, 
    db: Session = Depends(get_db)
):
    """
    Create a new project.
    """
    created_by_id = None
    if hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
    return create_project(db, project_in, created_by_id=created_by_id)

@router.put(
    "/projects/{project_id}", 
    response_model=ProjectOut,
    summary="Update an existing project",
    tags=["projects"],
    description="""
    Update an existing project's details by its ID.
    
    This endpoint allows modifying various attributes of a project such as title,
    description, status, language, region, and other configuration settings.
    
    Only the fields that are included in the request body will be updated; others will remain unchanged.
    """
)
def update_existing_project(
    project_id: int = Path(..., ge=1, description="The ID of the project to update"),
    project_in: ProjectUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update a project by its ID.
    """
    project = update_project(db, project_id, project_in)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.delete(
    "/projects/{project_id}", 
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a project (soft delete by marking as archived)",
    tags=["projects"],
    description="""
    Delete a project by its ID. 
    
    This is a soft delete operation that marks the project as archived rather than 
    permanently removing it from the database. Archived projects will not appear in regular
    project listings but can still be accessed directly if needed.
    
    Associated data such as personas, prompts, and calls are retained.
    """
)
def delete_existing_project(
    project_id: int = Path(..., ge=1, description="The ID of the project to delete"),
    db: Session = Depends(get_db)
):
    """
    Delete a project by its ID. By default, this is a soft delete that marks the project as archived.
    """
    success = delete_project(db, project_id)
    if not success:
        raise HTTPException(status_code=404, detail="Project not found")
    return None

from fastapi import APIRouter

router = APIRouter()

@router.get("/dashboard/summary")
def get_dashboard_summary():
    return {"summary": "Dummy dashboard summary"}

@router.get("/calls/logs")
def get_call_logs():
    return {"logs": ["Dummy log entry"]}

@router.get("/calls/responses")
def get_call_responses():
    return {"responses": ["Dummy response"]}

@router.get("/analytics")
def get_analytics():
    return {"analytics": "Dummy analytics"}

@router.get("/export/calls")
def export_calls():
    return {"export": "Dummy export"}

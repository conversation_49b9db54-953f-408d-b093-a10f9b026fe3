from fastapi import APIRouter, status
from pydantic import BaseModel

router = APIRouter()

class ValidateCallRequest(BaseModel):
    phone_number: str

@router.post("/calls/validate")
def validate_call(request: ValidateCallRequest):
    return {"message": "Validated (dummy)", "data": request.dict()}

@router.post("/calls/fallback")
def fallback_call():
    return {"message": "Fallback triggered (dummy)"}

@router.get("/calls/stream")
def stream_calls():
    return {"stream": "Dummy real-time transcription feed"}

@router.post("/api/velynk/push")
def push_to_velynk():
    return {"message": "Push to Velynk API (dummy)"}

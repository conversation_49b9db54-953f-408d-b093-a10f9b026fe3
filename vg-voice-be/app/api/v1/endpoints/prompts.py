from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request, Query, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.schemas.prompt import (
    PromptCreate,
    PromptUpdate,
    PromptOut,
    PromptListResponse,
    PromptListResponseMetadata,
    PromptStatsResponse,
    PromptVersionCreate,
    PromptVersionOut,
    PromptVersionUpdate
)
from app.models.prompt import PromptStatus, PromptType
from app.services.prompt_service import (
    get_prompts,
    get_prompt,
    create_prompt,
    update_prompt,
    get_prompt_stats
)
from app.services.prompt_version_service import (
    get_prompt_version,
    get_prompt_versions,
    create_prompt_version,
    update_prompt_version
)
from app.db import get_db

router = APIRouter()

# Prompt endpoints
@router.get(
    "/prompts", 
    response_model=PromptListResponse,
    summary="Get all prompts with filtering and search options",
    tags=["prompts"],
    description="""
    Get a list of all prompts with pagination, filtering and search options.
    Each prompt includes its current active version details.
    """
)
def list_prompts(
    persona_id: Optional[int] = Query(None, description="Filter prompts by persona ID"),
    project_id: Optional[int] = Query(None, description="Filter prompts by project ID"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term to filter prompts by text, type, etc."),
    status: Optional[str] = Query(None, description="Filter prompts by status"),
    type: Optional[str] = Query(None, description="Filter prompts by type"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get list of prompts with filtering, search and pagination options.
    Each prompt includes its current active version details.
    """
    # Build filters dict from query parameters
    filters = {}
    if persona_id:
        filters["persona_id"] = persona_id
    if status:
        filters["status"] = status
    if type:
        filters["type"] = type
    if project_id:
        filters["project_id"] = project_id
        
    prompts, total = get_prompts(
        db=db, 
        skip=skip, 
        limit=limit,
        filters=filters,
        search=search,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    # Convert to response models
    items_out = [PromptOut.model_validate(item) for item in prompts]
    
    metadata = PromptListResponseMetadata(
        total=total,
        skip=skip,
        limit=limit,
        has_more=total > (skip + limit),
        filters_applied=filters if filters else None
    )
    
    return PromptListResponse(
        metadata=metadata,
        items=items_out
    )

@router.get(
    "/prompts/statistics", 
    response_model=PromptStatsResponse,
    summary="Get prompt statistics",
    tags=["statistics"],
    description="Get aggregated statistics about prompts, grouped by status, persona, etc."
)
def get_prompts_stats(db: Session = Depends(get_db)):
    """
    Get statistics about prompts, grouped by status, persona, etc.
    """
    return get_prompt_stats(db)

@router.get(
    "/prompts/{prompt_id}", 
    response_model=PromptOut,
    summary="Get a specific prompt by ID",
    tags=["prompts"],
    description="Get detailed information about a specific prompt by its ID, including its active version."
)
def read_prompt(
    prompt_id: int = Path(..., ge=1, description="The ID of the prompt to get"),
    db: Session = Depends(get_db)
):
    """
    Get a prompt by its ID, including its current version.
    """
    prompt = get_prompt(db, prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    return prompt

@router.post(
    "/prompts", 
    response_model=PromptOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new prompt",
    tags=["prompts"],
    description="""
    Create a new prompt with its first version.
    The first version is automatically set as Active.
    """
)
def create_new_prompt(
    prompt_in: PromptCreate, 
    request: Request, 
    db: Session = Depends(get_db)
):
    """
    Create a new prompt with its first version.
    """
    created_by_id = None
    if hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
    return create_prompt(db, prompt_in, created_by_id=created_by_id)

@router.put(
    "/prompts/{prompt_id}", 
    response_model=PromptOut,
    summary="Update an existing prompt",
    tags=["prompts"],
    description="""
    Update a prompt's base properties (not version-specific details).
    This only updates the prompt record itself, not its versions.
    Use the version-specific endpoints to update version details.
    """
)
def update_existing_prompt(
    prompt_id: int = Path(..., ge=1, description="The ID of the prompt to update"),
    prompt_in: PromptUpdate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Update a prompt's base properties (not version-specific details).
    """
    updated_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        updated_by_id = request.state.user.get("id")
        
    prompt = update_prompt(db, prompt_id, prompt_in, updated_by_id=updated_by_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    return prompt

# Prompt Version endpoints
@router.get(
    "/prompts/{prompt_id}/versions", 
    response_model=List[PromptVersionOut],
    summary="Get all versions of a specific prompt",
    tags=["prompt versions"],
    description="Get all versions of a specific prompt, with sorting and pagination options."
)
def list_prompt_versions(
    prompt_id: int = Path(..., ge=1, description="The ID of the prompt"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    sort_by: str = Query("version", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get all versions of a specific prompt.
    """
    # First check if prompt exists
    prompt = get_prompt(db, prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
        
    versions, _ = get_prompt_versions(
        db,
        prompt_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    return [PromptVersionOut.model_validate(v) for v in versions]

@router.get(
    "/prompts/versions/{version_id}", 
    response_model=PromptVersionOut,
    summary="Get a specific prompt version",
    tags=["prompt versions"],
    description="Get detailed information about a specific prompt version by its ID."
)
def read_prompt_version(
    version_id: int = Path(..., ge=1, description="The ID of the prompt version to get"),
    db: Session = Depends(get_db)
):
    """
    Get a specific prompt version by its ID.
    """
    version = get_prompt_version(db, version_id)
    if not version:
        raise HTTPException(status_code=404, detail="Prompt version not found")
    return version

@router.post(
    "/prompts/{prompt_id}/versions", 
    response_model=PromptVersionOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new version for a prompt",
    tags=["prompt versions"],
    description="""
    Create a new version for an existing prompt.
    If status is set to Active, all other versions of this prompt will be set to Archived.
    Only one version can be active at a time per prompt.
    """
)
def create_new_prompt_version(
    prompt_id: int = Path(..., ge=1, description="The ID of the prompt"),
    version_in: PromptVersionCreate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Create a new version for an existing prompt.
    """
    # First check if prompt exists
    prompt = get_prompt(db, prompt_id)
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
        
    created_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
        
    return create_prompt_version(db, prompt_id, version_in, created_by_id=created_by_id)

@router.put(
    "/prompts/versions/{version_id}", 
    response_model=PromptVersionOut,
    summary="Update an existing prompt version",
    tags=["prompt versions"],
    description="""
    Update an existing prompt version.
    If status is changed to Active, all other versions of this prompt will be set to Archived.
    Only one version can be active at a time per prompt.
    """
)
def update_existing_prompt_version(
    version_id: int = Path(..., ge=1, description="The ID of the prompt version to update"),
    version_in: PromptVersionUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update an existing prompt version.
    """
    version = update_prompt_version(db, version_id, version_in)
    if not version:
        raise HTTPException(status_code=404, detail="Prompt version not found")
    return version

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime

from app.utils.logger import get_logger
from app.db import get_db
from app.models.user import User
from app.schemas.user import UserProfile, GoogleAuthInput
from app.services.user_service import (
    authenticate_user,
    create_access_token,
    get_user_by_email,
    verify_google_token,
    get_user_by_id
)

logger = get_logger(__name__)

router = APIRouter()

@router.post(
    "/users/google_authorization",
    response_model=UserProfile,
    summary="Login with Google OAuth",
    tags=["authentication"],
    description="""
    Login using Google authentication for Vegrow employees.
    
    This endpoint validates the Google ID token and ensures the user belongs to
    the vegrow.in domain. If valid, it either logs in an existing user or creates
    a new user record based on the Google account information.
    
    Returns a JWT token that can be used for authenticated requests, along with basic user information.
    """
)
def google_authorization(auth_data: GoogleAuthInput, db: Session = Depends(get_db)):
    """
    Authenticate with Google OAuth.
    """
    # Verify the Google token
    google_data = verify_google_token(auth_data.id_token)
    if not google_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Google Sign in failed or user is not from vegrow.in domain",
            headers={"WWW-Authenticate": "Bearer"},
        )
    logger.info(f"Google data: {google_data}")
    
    # Check if user exists
    email = google_data.get("email")
    user = get_user_by_email(db, email)
    
    if not user:
        # Create a new user if not exists
        user = User(
            email=email,
            username=email,
            first_name=google_data.get("name") or email.split('@')[0],
            is_active=True,
            password=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
    logger.info(f"User found or created: {user}")
    
    # Create access token
    token_data = {"sub": str(user.id)}
    access_token = create_access_token(token_data)
    logger.info(f"Access token created: {access_token}")
    
    # Create a response with the user data
    response = JSONResponse(
        content=jsonable_encoder(user),
        status_code=status.HTTP_200_OK
    )
    
    # Set the access token in the header
    response.headers["Authorization"] = f"Bearer {access_token}"
    
    return response

@router.get(
    "/users/current_user_profile",
    response_model=UserProfile,
    summary="Get current user profile",
    tags=["users"],
    description="""
    Get profile information for the currently authenticated user.
    
    This endpoint requires authentication with a valid JWT token.
    """
)
def current_user_profile(request: Request = None, db: Session = Depends(get_db)):
    """
    Get current user profile.
    """
    user_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        user_id = request.state.user.get("id")
        
    user = get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.post(
    "/users/logout",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Logout current user",
    tags=["authentication"],
    description="""
    Logout the current user.
    
    Note: Since JWT tokens are stateless, this endpoint doesn't actually invalidate the token.
    Client-side, the token should be discarded.
    """
)
def logout():
    """
    Logout current user.
    
    Note: This doesn't invalidate the JWT token, it should be discarded client-side.
    """
    # JWT tokens are stateless, so we can't really "invalidate" them server-side
    # The client should discard the token
    return None
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Request, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
import json
from typing import Optional
from sqlalchemy.orm import Session
from app.db import get_db
from app.config.envconfig import settings
from app.utils.logger import get_logger
from app.services.exotel_service import ExotelService
from app.schemas.exotel import ExotelInitiateCallResponse
from app.models.call import Call, CallStatus, CallType
from app.custom_call.serializers.exotel import ExotelFrameSerializer
from app.custom_call.pipelines.websocket_audio_2_audio import websocket_audio_2_audio_pipeline
from app.jobs.custom_call_post_processing import CustomCallPostProcessor

router = APIRouter()

logger = get_logger("request_logger")

# Remove bearer token authentication and use a basic API key authentication

@router.get(
    "/exotel/get_call_ws_url", 
    response_model=ExotelInitiateCallResponse,
    summary="Initiate callback from Exotel",
    tags=["exotel"],
    description="""
    This endpoint is used to initiate a call from Exotel. It can be called via GET or POST method.
    """
)
@router.post(
    "/exotel/get_call_ws_url", 
    response_model=ExotelInitiateCallResponse,
    summary="Initiate callback from Exotel",
    tags=["exotel"],
    description="""
    This endpoint is used to initiate a call from Exotel. It can be called via GET or POST method.
    """
)
async def get_call_ws_url(
    CallSid: Optional[str] = None,
):
    try:
        return await ExotelService().format_websocket_url(call_id=CallSid)
    except Exception as e:
        logger.error(f"Error in get_call_ws_url: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.websocket("/ws/{vendor}/{call_id}/{api_key}")
async def audio_websocket(
    websocket: WebSocket, 
    call_id: str,
    api_key: str,
    db: Session = Depends(get_db)
):
    try:
        if api_key != settings.third_party_api_key:
            await websocket.close(code=4003, reason="Invalid API key")
            return
        
        await websocket.accept()
        logger.info(f"WebSocket connection accepted")
        
        start_data = websocket.iter_text()
        await start_data.__anext__()
        
        call_data = json.loads(await start_data.__anext__())
        logger.info(f"Call data: {call_data}")
        
        stream_sid = call_data["start"]["stream_sid"]
        if not stream_sid:
            logger.error("No stream_sid in call data")
            await websocket.close(code=4000, reason="Missing stream_sid")
            return
        logger.info(f"Stream SID: {stream_sid}")
        
        # get call, prompt version, persona version
        call = db.query(Call).filter(Call.request_id == call_id).first()
        if not call:
            logger.error(f"Call not found for stream_sid: {call_id}")
            await websocket.close(code=4001, reason="Call not found")
            return
        
        if call.status != CallStatus.IN_PROGRESS:
            call.status = CallStatus.IN_PROGRESS
            db.commit()
            
        prompt_version = call.prompt_version
        persona_version = call.persona_version
        
        serializer = ExotelFrameSerializer(stream_sid)
        
        await websocket_audio_2_audio_pipeline(
            call=call,
            prompt_version=prompt_version,
            persona_version=persona_version,
            websocket_client=websocket,
            serializer=serializer
        )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for call {call_id}")
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")
        logger.exception("Full traceback:")
        try:
            await websocket.close(code=1011, reason=str(e))
        except:
            pass

@router.post(
    "/exotel/status_callback", 
    summary="Callback from Exotel",
    tags=["exotel"],
    description="""
    This endpoint is used to receive status callback from Exotel. It should called via GET or POST method.
    """
)
async def exotel_status_callback(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    try:
        data = await request.json()
        call_sid = data.get("CallSid")
        status = data.get("Status")

        if not call_sid:
            raise HTTPException(status_code=400, detail="Missing CallSid")

        call = db.query(Call).filter(Call.request_id == call_sid).first()
        if not call:
            logger.error(f"Call not found for status callback: {call_sid}")
            raise HTTPException(status_code=404, detail="Call not found")
        
        # Update call status
        call.status = status
        db.add(call)
        db.commit()
        db.refresh(call)

        # If completed, trigger post-processing
        if status == CallStatus.COMPLETED:
            background_tasks.add_task(CustomCallPostProcessor(db).process, call, with_time_out=300)
        return JSONResponse(content={"detail": f"Post processing triggered in background for call_id={call.id}"})
    except Exception as e:
        logger.error(f"Error in exotel_status_callback: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
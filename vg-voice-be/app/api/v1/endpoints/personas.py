from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.schemas.persona import (
    PersonaCreate,
    PersonaUpdate,
    PersonaOut,
    PersonaListResponse,
    PersonaListResponseMetadata,
    PersonaStatsResponse,
    PersonaVersionCreate,
    PersonaVersionOut,
    PersonaVersionUpdate
)
from app.models.persona import PersonaStatus
from app.services.persona_service import (
    get_personas,
    get_persona,
    create_persona,
    update_persona,
    get_persona_stats
)
from app.services.persona_version_service import (
    get_persona_version,
    get_persona_versions,
    create_persona_version,
    update_persona_version
)
from app.db import get_db

router = APIRouter()

# Persona endpoints
@router.get(
    "/personas", 
    response_model=PersonaListResponse,
    summary="Get all personas with filtering and search options",
    tags=["personas"],
    description="""
    Get a list of all personas with pagination, filtering and search options.
    Each persona includes its current active version details.
    """
)
def list_personas(
    project_id: Optional[int] = Query(None, description="Filter personas by project ID"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term to filter personas by name, language, etc."),
    status: Optional[str] = Query(None, description="Filter personas by status"),
    language: Optional[str] = Query(None, description="Filter personas by language"),
    voice_type: Optional[str] = Query(None, description="Filter personas by voice type"),
    engine: Optional[str] = Query(None, description="Filter personas by engine"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get list of personas with filtering, search and pagination options.
    Each persona includes its current active version details.
    """
    # Build filters dict from query parameters
    filters = {}
    if project_id:
        filters["project_id"] = project_id
    if status:
        filters["status"] = status
    if language:
        filters["language"] = language
    if voice_type:
        filters["voice_type"] = voice_type
    if engine:
        filters["engine"] = engine
        
    personas, total = get_personas(
        db=db, 
        skip=skip, 
        limit=limit,
        filters=filters,
        search=search,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    # Convert to response models
    items_out = [PersonaOut.model_validate(item) for item in personas]
    
    metadata = PersonaListResponseMetadata(
        total=total,
        skip=skip,
        limit=limit,
        has_more=total > (skip + limit),
        filters_applied=filters if filters else None
    )
    
    return PersonaListResponse(
        metadata=metadata,
        items=items_out
    )

@router.get(
    "/personas/statistics", 
    response_model=PersonaStatsResponse,
    summary="Get persona statistics",
    tags=["statistics"],
    description="Get aggregated statistics about personas, grouped by status, project, etc."
)
def get_personas_stats(db: Session = Depends(get_db)):
    """
    Get statistics about personas, grouped by status, project, etc.
    """
    return get_persona_stats(db)

@router.get(
    "/personas/{persona_id}", 
    response_model=PersonaOut,
    summary="Get a specific persona by ID",
    tags=["personas"],
    description="Get detailed information about a specific persona by its ID, including its active version."
)
def read_persona(
    persona_id: int = Path(..., ge=1, description="The ID of the persona to get"),
    db: Session = Depends(get_db)
):
    """
    Get a persona by its ID, including its current version.
    """
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail="Persona not found")
    return persona

@router.post(
    "/personas", 
    response_model=PersonaOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new persona",
    tags=["personas"],
    description="""
    Create a new persona with its first version.
    The first version is automatically set as Active.
    If is_default is true, other personas in the project will be archived.
    """
)
def create_new_persona(
    persona_in: PersonaCreate, 
    request: Request, 
    db: Session = Depends(get_db)
):
    """
    Create a new persona with its first version.
    """
    created_by_id = None
    if hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
    return create_persona(db, persona_in, created_by_id=created_by_id)

@router.put(
    "/personas/{persona_id}", 
    response_model=PersonaOut,
    summary="Update an existing persona",
    tags=["personas"],
    description="""
    Update a persona's base properties (not version-specific details).
    This only updates the persona record itself, not its versions.
    Use the version-specific endpoints to update version details.
    """
)
def update_existing_persona(
    persona_id: int = Path(..., ge=1, description="The ID of the persona to update"),
    persona_in: PersonaUpdate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Update a persona's base properties (not version-specific details).
    """
    updated_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        updated_by_id = request.state.user.get("id")
        
    persona = update_persona(db, persona_id, persona_in, updated_by_id=updated_by_id)
    if not persona:
        raise HTTPException(status_code=404, detail="Persona not found")
    return persona

# Persona Version endpoints
@router.get(
    "/personas/{persona_id}/versions", 
    response_model=List[PersonaVersionOut],
    summary="Get all versions of a specific persona",
    tags=["persona versions"],
    description="Get all versions of a specific persona, with sorting and pagination options."
)
def list_persona_versions(
    persona_id: int = Path(..., ge=1, description="The ID of the persona"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    sort_by: str = Query("version", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get all versions of a specific persona.
    """
    # First check if persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail="Persona not found")
        
    versions, _ = get_persona_versions(
        db,
        persona_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    return [PersonaVersionOut.model_validate(v) for v in versions]

@router.get(
    "/personas/versions/{version_id}", 
    response_model=PersonaVersionOut,
    summary="Get a specific persona version",
    tags=["persona versions"],
    description="Get detailed information about a specific persona version by its ID."
)
def read_persona_version(
    version_id: int = Path(..., ge=1, description="The ID of the persona version to get"),
    db: Session = Depends(get_db)
):
    """
    Get a specific persona version by its ID.
    """
    version = get_persona_version(db, version_id)
    if not version:
        raise HTTPException(status_code=404, detail="Persona version not found")
    return version

@router.post(
    "/personas/{persona_id}/versions", 
    response_model=PersonaVersionOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new version for a persona",
    tags=["persona versions"],
    description="""
    Create a new version for an existing persona.
    If status is set to Active, all other versions of this persona will be set to Archived.
    Only one version can be active at a time per persona.
    """
)
def create_new_persona_version(
    persona_id: int = Path(..., ge=1, description="The ID of the persona"),
    version_in: PersonaVersionCreate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Create a new version for an existing persona.
    """
    # First check if persona exists
    persona = get_persona(db, persona_id)
    if not persona:
        raise HTTPException(status_code=404, detail="Persona not found")
        
    created_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
        
    return create_persona_version(db, persona_id, version_in, created_by_id=created_by_id)

@router.put(
    "/personas/versions/{version_id}", 
    response_model=PersonaVersionOut,
    summary="Update an existing persona version",
    tags=["persona versions"],
    description="""
    Update an existing persona version.
    If status is changed to Active, all other versions of this persona will be set to Archived.
    Only one version can be active at a time per persona.
    """
)
def update_existing_persona_version(
    version_id: int = Path(..., ge=1, description="The ID of the persona version to update"),
    version_in: PersonaVersionUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update an existing persona version (typically used for draft versions before they're activated).
    """
    version = update_persona_version(db, version_id, version_in)
    if not version:
        raise HTTPException(status_code=404, detail="Persona version not found")
    return version

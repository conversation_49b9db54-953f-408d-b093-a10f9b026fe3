from fastapi import APIRouter, status, Depends, HTTPException, Query, Request, Path, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import csv
from io import StringIO

from sqlalchemy.orm import Session
from sqlalchemy import func, desc, asc

from app.db import get_db
from app.models.call import Call, CallStatus, CallType, PostProcessingStatus
from app.models.call_batch import CallBatch
from app.models.persona import Persona, PersonaEngine, PersonaVersion, PersonaStatus
from app.models.prompt import Prompt, PromptType, PromptStatus, PromptVersion
from app.models.questionnaire import Questionnaire, QuestionnaireStatus, QuestionnaireVersion
from app.models.call_response import CallResponse
from app.services.exotel_service import ExotelService
from app.services.prompt_service import get_active_prompt_version, get_variables_from_prompt_text
from app.utils.logger import get_logger
from app.jobs.custom_call_post_processing import CustomCallPostProcessor
from app.schemas.call import (
    CallTriggerRequest,
    CallTriggerCustomRequest,
    CallResponseRequest,
    CallListResponse,
    CallListResponseMetadata,
    CallDetails,
    CallBatchDetails,
    CallStatistics,
    CallResponseUpdateRequest,
    CallResponseUpdateBulkRequest,
    CallResponsesUpdateRequest
)
from app.config.envconfig import settings

router = APIRouter()
logger = get_logger("call_endpoints")


@router.post(
    "/calls/trigger", 
    status_code=status.HTTP_202_ACCEPTED,
    tags=["calls"],
    summary="Trigger calls with default/custom persona settings",
    description="""
    This endpoint triggers calls using the default persona settings or custom persona settings.
    It accepts a request body with the project ID, phone number, and optional persona version ID.
    The call is initiated using the Exotel service.
    """
)
async def trigger_calls(
    request: CallTriggerRequest, 
    db: Session = Depends(get_db),
    fastapi_request: Request = None
):
    """Trigger multiple calls with the default persona settings"""
    # Get the active persona version
    active_version = None
    if not request.persona_version_id:
        default_persona = db.query(Persona).filter(
            Persona.project_id == request.project_id,
            Persona.is_default == True,
        ).first()
        
        if not default_persona:
            raise HTTPException(status_code=404, detail="No default persona found for this project")
        active_version = db.query(PersonaVersion).filter(
            PersonaVersion.persona_id == default_persona.id,
            PersonaVersion.status == PersonaStatus.ACTIVE,
        ).first()
    else:  
        active_version = db.query(PersonaVersion).filter(
            PersonaVersion.id == request.persona_version_id
        ).first()
        
    if not active_version:
        raise HTTPException(status_code=400, detail=f"No persona version found for id {request.persona_version_id}")
    
    # Check if the engine is CUSTOM_AI
    if active_version.engine != PersonaEngine.CUSTOM:
        raise HTTPException(status_code=400, detail=f"Only CUSTOM_AI engine is currently supported. '{active_version.engine}' engine is not integrated yet.")
    
    # get active conversational prompt version
    active_conversational_prompt = db.query(Prompt).filter(
        Prompt.persona_id == active_version.persona_id,
        Prompt.status == PromptStatus.ACTIVE,
        Prompt.type == PromptType.CONVERSATIONAL,
    ).first()
    if not active_conversational_prompt:
        raise HTTPException(status_code=400, detail=f"No active conversational prompt found for persona {active_version.persona_id}")
    
    active_conversational_prompt_version = get_active_prompt_version(
        db, active_conversational_prompt.id
    )
    if not active_conversational_prompt_version:
        raise HTTPException(status_code=400, detail=f"No active conversational prompt version found for prompt {active_conversational_prompt.id}")
    
    # cross check if all variables are present in the request
    variables = get_variables_from_prompt_text(active_conversational_prompt_version.prompt_text)
    for var in variables:
        if var not in request.variables:
            raise HTTPException(status_code=400, detail=f"Missing required variable: {var}")
    
    
    # TODO
    # validate phone number
    # persona_id & prompt_id validation
    # from number & flow id validation
    
    call = Call(
        project_id=request.project_id,
        persona_version_id=active_version.id,
        conversational_prompt_version_id=active_conversational_prompt_version.id,
        call_type=CallType.OUTBOUND,
        to_number=request.phone_number,
        from_number=active_version.from_number,
        variables=request.variables if request.variables else {},
        status=CallStatus.IN_QUEUE,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by_id=fastapi_request.state.user.get("id") if fastapi_request and hasattr(fastapi_request.state, "user") else None
    )
    db.add(call)
    db.commit()
    db.refresh(call)

    # Create CallResponses for all active questionnaires questions for this call
    active_questionnaires = db.query(Questionnaire).filter(
        Questionnaire.project_id == request.project_id,
        Questionnaire.status == QuestionnaireStatus.ACTIVE
    ).all()

    for questionnaire in active_questionnaires:
        question_version = db.query(QuestionnaireVersion).filter(
            QuestionnaireVersion.questionnaire_id == questionnaire.id,
            QuestionnaireVersion.status == QuestionnaireStatus.ACTIVE
        ).first()

        call_response = CallResponse(
            call_id=call.id,
            questionnaire_version_id=question_version.id,
            created_at=datetime.now()
        )
        
        db.add(call_response)
    db.commit()
    
    try:
        flow_id = settings.from_number_flow_id_map.get(active_version.from_number)
        if not flow_id:
            raise HTTPException(status_code=400, detail=f"Flow ID not found for from_number {active_version.from_number}")
        
        exotel_service = ExotelService()
        call_response = await exotel_service.make_call_via_flow(
            to_number=request.phone_number,
            from_number=active_version.from_number,
            flow_id=flow_id,
            recording=True
        )
        
        if call_response.get("status") == "error":
            raise HTTPException(status_code=500, detail=f"Failed to trigger call: {call_response.get('message')}")
        
        # Update the call with the Exotel call ID
        call.request_id = call_response.get("call_sid")
        call.status = CallStatus.INITIATED
        db.commit()
        
    except Exception as e:
        logger.error(f"Failed to trigger call to {request.phone_number}: {str(e)}")
        call.status = CallStatus.FAILED
        call.failure_reason = str(e)[:100]
        db.commit()
    
    return {
        "message": f"Call triggered for {request.phone_number}!",
        "call_id": call.id
    }

@router.get("/calls", response_model=CallListResponse)
def get_calls(
    skip: int = 0,
    limit: int = 100,
    project_id: Optional[int] = None,
    batch_id: Optional[int] = None,
    status: Optional[str] = None,
    sort_by: str = "created_at",
    sort_desc: bool = True,
    db: Session = Depends(get_db)
):
    """Get a list of calls with filtering options"""
    # Build the query with filters
    query = db.query(
        Call, 
        Persona.name.label('persona_name')
    ).outerjoin(
        PersonaVersion, Call.persona_version_id == PersonaVersion.id
    ).outerjoin(
        Persona, PersonaVersion.persona_id == Persona.id
    )
    
    filters = {}
    
    if project_id:
        query = query.filter(Call.project_id == project_id)
        filters["project_id"] = project_id
        
    if batch_id:
        query = query.filter(Call.call_batch_id == batch_id)
        filters["batch_id"] = batch_id
        
    if status:
        query = query.filter(Call.status == status)
        filters["status"] = status
    
    # Get the total count - need to use a subquery for accurate count with joins
    total_query = query.with_entities(Call.id)
    total = total_query.count()
    
    # Apply sorting
    if sort_by:
        column = getattr(Call, sort_by, None)
        if column:
            query = query.order_by(desc(column) if sort_desc else asc(column))
    
    # Apply pagination
    query_results = query.offset(skip).limit(limit).all()
    
    # Convert to response model
    items = [
        CallDetails(
            id=call.id,
            to_number=call.to_number,
            status=call.status,
            duration_seconds=call.duration_seconds,
            created_at=call.created_at,
            audio_url=call.audio_url,
            failure_reason=call.failure_reason,
            persona_name=persona_name
        )
        for call, persona_name in query_results
    ]
    
    metadata = CallListResponseMetadata(
        total=total,
        skip=skip,
        limit=limit,
        has_more=total > (skip + limit),
        filters_applied=filters if filters else None
    )
    
    return CallListResponse(
        metadata=metadata,
        items=items
    )

@router.get("/calls/{call_id}", response_model=Dict[str, Any])
def get_call_details(call_id: int, db: Session = Depends(get_db)):
    """Get detailed information about a specific call"""
    call = db.query(Call).filter(Call.id == call_id).first()
    if not call:
        raise HTTPException(status_code=404, detail=f"Call with ID {call_id} not found")
    
    # Get persona version details
    persona_version = db.query(PersonaVersion).filter(
        PersonaVersion.id == call.persona_version_id
    ).first()
    persona = persona_version.persona
    
    prompt_version = db.query(PromptVersion).filter(
        PromptVersion.id == call.conversational_prompt_version_id
    ).first()
    prompt = prompt_version.prompt
    
    # Get call responses if any
    call_responses = db.query(CallResponse).filter(
        CallResponse.call_id == call_id
    ).all()
    
    # TODO: add mongo data here
    return {
        "id": call.id,
        "project_id": call.project_id,
        "call_type": call.call_type,
        "to_number": call.to_number,
        "from_number": call.from_number,
        "status": call.status,
        "duration_seconds": call.duration_seconds,
        "created_at": call.created_at,
        "updated_at": call.updated_at,
        "audio_url": call.audio_url,
        "failure_reason": call.failure_reason,
        "variables": call.variables,
        "persona": {
            "id": persona.id if persona else None,
            "name": persona.name if persona else None,
            "version": {
                "id": persona_version.id if persona_version else None,
                "engine": persona_version.engine if persona_version else None,
                "voice_type": persona_version.voice_type if persona_version else None,
                "language": persona_version.language if persona_version else None
            }
        },
        "prompt": {
            "id": prompt.id if prompt else None,
            "type": prompt.type if prompt else None,
            "version": {
                "id": prompt_version.id if prompt_version else None,
                "prompt_text": prompt_version.prompt_text if prompt_version else None,
                "version": prompt_version.version if prompt_version else None,
                "model": prompt_version.model if prompt_version else None
            }
        },
        "responses": [
            {
                "id": response.id,
                "question_text": response.questionnaire_version.question_text,
                "answer_text": response.answer_text
            }
            for response in call_responses
        ] if call_responses else []
    }

@router.post("/calls/{call_id}/retry", status_code=status.HTTP_202_ACCEPTED)
async def retry_call(
    call_id: int, 
    db: Session = Depends(get_db),
    fastapi_request: Request = None
):
    """Retry a failed call"""
    call = db.query(Call).filter(Call.id == call_id).first()
    if not call:
        raise HTTPException(status_code=404, detail=f"Call with ID {call_id} not found")
    
    # Check if the call failed (so it can be retried)
    if call.status not in CallStatus.ALLOW_RETRY:
        raise HTTPException(status_code=400, detail=f"Only failed calls can be retried. Current status: {call.status}")
    
    # Get the persona version
    persona_version = db.query(PersonaVersion).filter(PersonaVersion.id == call.persona_version_id).first()
    if not persona_version:
        raise HTTPException(status_code=404, detail="Persona version not found")
    
    # Check if the engine is CUSTOM_AI
    if persona_version.engine != PersonaEngine.CUSTOM:
        raise HTTPException(status_code=400, detail=f"Only CUSTOM_AI engine is currently supported. '{persona_version.engine}' engine is not integrated yet.")
    
    # Trigger the call via Exotel
    exotel_service = ExotelService()
    try:
        flow_id = settings.from_number_flow_id_map.get(persona_version.from_number)
        if not flow_id:
            raise HTTPException(status_code=400, detail=f"Flow ID not found for from_number {persona_version.from_number}")
        
        call_response = await exotel_service.make_call_via_flow(
            to_number=call.to_number,
            from_number=persona_version.from_number,
            flow_id=flow_id,
            recording=True
        )
        
        if call_response.get("status") == "error":
            raise HTTPException(status_code=500, detail=f"Failed to trigger call: {call_response.get('message')}")
        
        # Update the call status
        call.status = CallStatus.INITIATED
        call.request_id = call_response.get("call_sid")
        call.updated_at = datetime.now()
        call.updated_by_id = fastapi_request.state.user.get("id") if fastapi_request and hasattr(fastapi_request.state, "user") else None
        db.commit()
        
        return {
            "message": f"Call to {call.to_number} retried successfully",
            "call_id": call.id,
            "status": call.status
        }
    except Exception as e:
        logger.error(f"Failed to retry call to {call.to_number}: {str(e)}")
        call.status = CallStatus.FAILED
        call.failure_reason = str(e)[:100]  # Truncate to fit the field
        call.updated_at = datetime.now()
        db.commit()
        
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to retry call: {str(e)}"
        )

@router.get("/call-statistics", response_model=CallStatistics)
def get_call_statistics(
    project_id: Optional[int] = None, 
    batch_id: Optional[int] = None,
    interval: Optional[int] = 1,
    db: Session = Depends(get_db)
):
    """Get call statistics for today and the past 7 days"""
    # Base query
    query = db.query(Call)
    
    if interval:
        start_date = datetime.now().date() - timedelta(days=interval - 1)
        query = query.filter(func.date(Call.created_at) >= start_date)
        
    if project_id:
        query = query.filter(Call.project_id == project_id)
    
    if batch_id:
        query = query.filter(Call.call_batch_id == batch_id)
    
    # Get overall statistics
    total_calls = query.count()
    completed_calls = query.filter(Call.status == CallStatus.COMPLETED).count()
    success_rate = (completed_calls / total_calls) * 100 if total_calls > 0 else 0
    avg_call_duration = query.with_entities(func.avg(Call.duration_seconds)).scalar() or 0
        
    return CallStatistics(
        total_calls=total_calls,
        completed_calls=completed_calls,
        avg_call_duration=avg_call_duration,
        success_rate=success_rate,
    )


@router.get("/call-status-count")
def get_call_status_count(
    project_id: Optional[int] = None,
    batch_id: Optional[int] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: Session = Depends(get_db)
):
    """
    Get call status counts by date for a bar graph visualization.
    If start_date and end_date are not provided, it defaults to the last 7 days.
    """
    # Set default date range to last 7 days if not provided
    if not end_date:
        end_date = datetime.now()
    if not start_date:
        start_date = end_date - timedelta(days=6)
    
    # Base query
    query = db.query(
        func.date(Call.created_at).label('date'),
        Call.status,
        func.count().label('count')
    )
    
    # Apply filters
    if project_id:
        query = query.filter(Call.project_id == project_id)
    
    if batch_id:
        query = query.filter(Call.call_batch_id == batch_id)
    
    # Filter by date range
    query = query.filter(
        Call.created_at >= start_date,
        Call.created_at <= end_date
    )
    
    # Group by date and status
    query = query.group_by(
        func.date(Call.created_at),
        Call.status
    ).order_by(func.date(Call.created_at))
    
    # Execute query
    results = query.all()
    
    # Organize data by date
    date_data = {}
    for date, status, count in results:
        date_str = date.isoformat() if date else None
        if date_str not in date_data:
            date_data[date_str] = {
                "total_calls": 0,
                "completed_calls": 0,
                "failed_calls": 0,
                "in_progress_calls": 0
            }
        
        date_data[date_str]["total_calls"] += count
        
        if status == CallStatus.COMPLETED:
            date_data[date_str]["completed_calls"] += count
        elif status in [CallStatus.FAILED, CallStatus.SYSTEM_FAILURE]:
            date_data[date_str]["failed_calls"] += count
        else:
            date_data[date_str]["in_progress_calls"] += count
    
    # Fill in any missing dates in the range
    current_date = start_date.date()
    end = end_date.date()
    
    while current_date <= end:
        date_str = current_date.isoformat()
        if date_str not in date_data:
            date_data[date_str] = {
                "total_calls": 0,
                "completed_calls": 0,
                "failed_calls": 0,
                "in_progress_calls": 0
            }
        current_date += timedelta(days=1)
    
    # return the dates response in ASC order
    sorted_date_data = dict(sorted(date_data.items()))
    
    return {"dates": sorted_date_data}

@router.patch("/calls/{call_id}/responses", status_code=status.HTTP_200_OK, tags=["calls"])
def update_call_responses(
    call_id: int,
    request: CallResponsesUpdateRequest,
    db: Session = Depends(get_db),
    fastapi_request: Request = None
):
    """
    Update answer texts for multiple call responses associated with a specific call.
    This API validates that all response IDs belong to the call ID provided in the path.
    """
    # First validate that the call exists
    call = db.query(Call).filter(Call.id == call_id).first()
    if not call:
        raise HTTPException(
            status_code=404, 
            detail=f"Call with ID {call_id} not found"
        )
    
    # Get all response IDs from the request
    response_ids = [update.response_id for update in request.updates]
    
    # Fetch all call responses in one query and validate they belong to this call
    call_responses = db.query(CallResponse).filter(
        CallResponse.id.in_(response_ids)
    ).all()
    
    # Create a lookup dictionary for quick access to call responses by ID
    response_lookup = {response.id: response for response in call_responses}
    
    # Verify all responses belong to this call and track the results
    results = {
        "success": [],
        "failed": []
    }
    
    for update in request.updates:
        response = response_lookup.get(update.response_id)
        
        # Check if response exists and belongs to this call
        if not response:
            results["failed"].append({
                "response_id": update.response_id,
                "reason": f"Call response with ID {update.response_id} not found"
            })
            continue
            
        if response.call_id != call_id:
            results["failed"].append({
                "response_id": update.response_id,
                "reason": f"Call response with ID {update.response_id} does not belong to call {call_id}"
            })
            continue
        
        # Update the answer text
        try:
            response.answer_text = update.answer_text
            results["success"].append({
                "response_id": update.response_id
            })
        except Exception as e:
            results["failed"].append({
                "response_id": update.response_id,
                "reason": str(e)
            })
    
    # Commit all changes at once if there are any successful updates
    if results["success"]:
        db.commit()
    
    # Include the call details in the response
    return {
        "call_id": call_id,
        "message": f"Updated {len(results['success'])} call responses, {len(results['failed'])} failed",
        "results": results
    }

@router.put(
    "/calls/retrigger_post_call_processing",
    status_code=status.HTTP_200_OK,
    tags=["calls"],
    summary="Retrigger post call processing for a call",
    description="Retriggers post-processing for a call by call_id. If the call's post-processing status is not pending, it sets it to pending and invokes post-processing."
)
async def retrigger_post_call_processing(
    background_tasks: BackgroundTasks,
    call_id: int = Query(..., description="ID of the call to retrigger post-processing for"),
    db: Session = Depends(get_db)
):
    """
    Retrigger post-processing for a call by call_id. If the call's post-processing status is not pending, it sets it to pending and invokes post-processing in the background.
    """
    logger.info(f"Retriggering post-processing for call_id={call_id}")
    call = db.query(Call).filter(Call.id == call_id).first()
    if not call:
        logger.error(f"Call not found for id={call_id}")
        raise HTTPException(status_code=404, detail=f"Call not found for id={call_id}")

    if call.status == CallStatus.COMPLETED:
        if call.post_processing_status != PostProcessingStatus.PENDING:
            logger.info(f"Call {call_id} post_processing_status is {call.post_processing_status}, setting to pending.")
            call.post_processing_status = PostProcessingStatus.PENDING
            db.add(call)
            db.commit()
            db.refresh(call)
        
        background_tasks.add_task(CustomCallPostProcessor(db).process, call)
    else:
        logger.info(f"Call {call_id} status is {call.status}, not completed. Post-processing not triggered.")
        raise HTTPException(
            status_code=400, 
            detail=f"Call {call_id} status is {call.status}, not completed. Post-processing not triggered."
        )

    return {"message": f"Post-processing re-triggered in background for call_id={call_id}"}

###################################### BELOW ARE NOT IMPLEMENTED YET ######################################

@router.post("/calls/upload-csv", status_code=status.HTTP_202_ACCEPTED)
async def upload_csv_for_calls(
    project_id: int,
    persona_id: int,
    csv_content: str,
    db: Session = Depends(get_db),
    fastapi_request: Request = None
):
    # """Upload a CSV file and trigger calls for the phone numbers it contains"""
    # # Get the persona
    # persona = db.query(Persona).filter(Persona.id == persona_id).first()
    # if not persona:
    #     raise HTTPException(status_code=404, detail=f"Persona with ID {persona_id} not found")
    
    # # Get the active persona version
    # active_version = db.query(PersonaVersion).filter(
    #     PersonaVersion.persona_id == persona.id,
    #     PersonaVersion.status == "Active"
    # ).first()
    
    # if not active_version:
    #     raise HTTPException(status_code=400, detail=f"No active version found for persona {persona.name}")
    
    # # Check if the engine is CUSTOM_AI
    # if active_version.engine != PersonaEngine.CUSTOM:
    #     raise HTTPException(status_code=400, detail=f"Only CUSTOM_AI engine is currently supported. '{active_version.engine}' engine is not integrated yet.")
    
    # # Parse the CSV
    # phone_numbers = []
    # variables_list = []
    
    # try:
    #     csv_file = StringIO(csv_content)
    #     csv_reader = csv.DictReader(csv_file)
        
    #     for row in csv_reader:
    #         if 'phone_number' not in row:
    #             raise HTTPException(status_code=400, detail="CSV must contain a 'phone_number' column")
            
    #         phone_number = row.pop('phone_number', None)
    #         if phone_number:
    #             phone_numbers.append(phone_number)
    #             variables_list.append(row)  # Store the remaining columns as variables
            
    # except Exception as e:
    #     raise HTTPException(status_code=400, detail=f"Error parsing CSV: {str(e)}")
    
    # if not phone_numbers:
    #     raise HTTPException(status_code=400, detail="No valid phone numbers found in the CSV")
    
    # # Create a call batch
    # call_batch = CallBatch(
    #     name=f"CSV Batch {datetime.now()}",
    #     status="Pending",
    #     created_at=datetime.now(),
    #     created_by_id=fastapi_request.state.user.get("id") if fastapi_request and hasattr(fastapi_request.state, "user") else None,
    #     updated_at=datetime.now()
    # )
    # db.add(call_batch)
    # db.commit()
    # db.refresh(call_batch)
    
    # # Create call entries
    # exotel_service = ExotelService()
    # created_calls = []
    
    # for i, phone_number in enumerate(phone_numbers):
    #     # Create a call entry
    #     call = Call(
    #         project_id=project_id,
    #         persona_version_id=active_version.id,
    #         conversational_prompt_version_id=1,  # This should be fetched from the persona's configuration
    #         call_batch_id=call_batch.id,
    #         call_type=CallType.OUTBOUND,
    #         to_number=phone_number,
    #         from_number=active_version.from_number,
    #         variables=variables_list[i] if i < len(variables_list) else {},
    #         status=CallStatus.IN_QUEUE,
    #         created_at=datetime.now(),
    #         updated_at=datetime.now(),
    #         created_by_id=fastapi_request.state.user.get("id") if fastapi_request and hasattr(fastapi_request.state, "user") else None
    #     )
    #     db.add(call)
    #     db.commit()
    #     db.refresh(call)
    #     created_calls.append(call)
        
    #     # Trigger the call via Exotel
    #     try:
    #         call_response = await exotel_service.make_call_via_flow(
    #             to_number=phone_number,
    #             from_number=active_version.from_number,
    #             flow_id="your-flow-id",  # This should be configured
    #             recording=True
    #         )
            
    #         # Update the call with the Exotel call ID
    #         call.request_id = call_response.get("call_sid")
    #         call.status = CallStatus.INITIATED
    #         db.commit()
            
    #     except Exception as e:
    #         logger.error(f"Failed to trigger call to {phone_number}: {str(e)}")
    #         call.status = CallStatus.FAILED
    #         call.failure_reason = str(e)[:100]  # Truncate to fit the field
    #         db.commit()
    
    # # Update batch status
    # call_batch.status = "Processing"
    # db.commit()
    
    # return {
    #     "message": f"Batch created with {len(created_calls)} calls from CSV upload",
    #     "batch_id": call_batch.id,
    #     "calls_initiated": len(created_calls)
    # }
    raise HTTPException(status_code=400, detail="CSV upload is not implemented yet")

@router.get("/call-batches/{batch_id}", response_model=Dict[str, Any])
def get_batch_details(batch_id: int, db: Session = Depends(get_db)):
    """Get detailed information about a call batch and its calls"""
    batch = db.query(CallBatch).filter(CallBatch.id == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail=f"Call batch with ID {batch_id} not found")
    
    # Get call statistics for this batch
    calls = db.query(Call).filter(Call.call_batch_id == batch_id).all()
    total_calls = len(calls)
    completed_calls = sum(1 for call in calls if call.status == CallStatus.COMPLETED)
    failed_calls = sum(1 for call in calls if call.status in [CallStatus.FAILED, CallStatus.SYSTEM_FAILURE])
    in_progress_calls = sum(1 for call in calls if call.status not in [CallStatus.COMPLETED, CallStatus.FAILED, CallStatus.SYSTEM_FAILURE])
    
    # Create a batch details response
    batch_details = {
        "id": batch.id,
        "name": batch.name,
        "status": batch.status,
        "created_at": batch.created_at,
        "updated_at": batch.updated_at,
        "statistics": {
            "total_calls": total_calls,
            "completed_calls": completed_calls,
            "failed_calls": failed_calls,
            "in_progress_calls": in_progress_calls,
            "success_rate": (completed_calls / total_calls) * 100 if total_calls > 0 else 0
        },
        "calls": [
            {
                "id": call.id,
                "to_number": call.to_number,
                "status": call.status,
                "duration_seconds": call.duration_seconds,
                "created_at": call.created_at,
                "audio_url": call.audio_url,
                "failure_reason": call.failure_reason
            }
            for call in calls
        ]
    }
    
    return batch_details

@router.get("/call-batches", response_model=List[CallBatchDetails])
def get_call_batches(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get a list of call batches with statistics"""
    batches = db.query(CallBatch).order_by(desc(CallBatch.created_at)).offset(skip).limit(limit).all()
    
    # Create a response with batch details and statistics
    result = []
    for batch in batches:
        # Get call statistics for this batch
        calls_query = db.query(Call).filter(Call.call_batch_id == batch.id)
        total_calls = calls_query.count()
        completed_calls = calls_query.filter(Call.status == CallStatus.COMPLETED).count()
        failed_calls = calls_query.filter(Call.status.in_([CallStatus.FAILED, CallStatus.SYSTEM_FAILURE])).count()
        in_progress_calls = total_calls - completed_calls - failed_calls
        
        result.append(
            CallBatchDetails(
                id=batch.id,
                name=batch.name,
                status=batch.status,
                created_at=batch.created_at,
                total_calls=total_calls,
                completed_calls=completed_calls,
                failed_calls=failed_calls,
                in_progress_calls=in_progress_calls
            )
        )
    
    return result
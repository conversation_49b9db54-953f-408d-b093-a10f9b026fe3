from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.schemas.questionnaire import (
    QuestionnaireCreate,
    QuestionnaireUpdate,
    QuestionnaireOut,
    QuestionnaireListResponse,
    QuestionnaireListResponseMetadata,
    QuestionnaireStatsResponse,
    QuestionnaireVersionCreate,
    QuestionnaireVersionOut,
    QuestionnaireVersionUpdate
)
from app.models.questionnaire import QuestionnaireStatus, QuestionType
from app.services.questionnaire_service import (
    get_questionnaires,
    get_questionnaire,
    create_questionnaire,
    update_questionnaire,
    get_questionnaire_stats
)
from app.services.questionnaire_version_service import (
    get_questionnaire_version,
    get_questionnaire_versions,
    create_questionnaire_version,
    update_questionnaire_version
)
from app.db import get_db

router = APIRouter()

# Questionnaire endpoints
@router.get(
    "/questionnaires", 
    response_model=QuestionnaireListResponse,
    summary="Get all questionnaires with filtering and search options",
    tags=["questionnaires"],
    description="""
    Get a list of all questionnaires with pagination, filtering and search options.
    Each questionnaire includes its current active version details.
    """
)
def list_questionnaires(
    project_id: Optional[int] = Query(None, description="Filter questionnaires by project ID"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term to filter questionnaires by text, type, etc."),
    status: Optional[str] = Query(None, description="Filter questionnaires by status"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get list of questionnaires with filtering, search and pagination options.
    Each questionnaire includes its current active version details.
    """
    # Build filters dict from query parameters
    filters = {}
    if project_id:
        filters["project_id"] = project_id
    if status:
        filters["status"] = status
        
    questionnaires, total = get_questionnaires(
        db=db, 
        skip=skip, 
        limit=limit,
        filters=filters,
        search=search,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    # Convert to response models
    items_out = [QuestionnaireOut.model_validate(item) for item in questionnaires]
    
    metadata = QuestionnaireListResponseMetadata(
        total=total,
        skip=skip,
        limit=limit,
        has_more=total > (skip + limit),
        filters_applied=filters if filters else None
    )
    
    return QuestionnaireListResponse(
        metadata=metadata,
        items=items_out
    )

@router.get(
    "/questionnaires/statistics", 
    response_model=QuestionnaireStatsResponse,
    summary="Get questionnaire statistics",
    tags=["statistics"],
    description="Get aggregated statistics about questionnaires, grouped by status, project, etc."
)
def get_questionnaires_stats(db: Session = Depends(get_db)):
    """
    Get statistics about questionnaires, grouped by status, project, etc.
    """
    return get_questionnaire_stats(db)

@router.get(
    "/questionnaires/{questionnaire_id}", 
    response_model=QuestionnaireOut,
    summary="Get a specific questionnaire by ID",
    tags=["questionnaires"],
    description="Get detailed information about a specific questionnaire by its ID, including its active version."
)
def read_questionnaire(
    questionnaire_id: int = Path(..., ge=1, description="The ID of the questionnaire to get"),
    db: Session = Depends(get_db)
):
    """
    Get a questionnaire by its ID, including its current version.
    """
    questionnaire = get_questionnaire(db, questionnaire_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")
    return questionnaire

@router.post(
    "/questionnaires", 
    response_model=QuestionnaireOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new questionnaire",
    tags=["questionnaires"],
    description="""
    Create a new questionnaire with its first version.
    The first version is automatically set as Active.
    """
)
def create_new_questionnaire(
    questionnaire_in: QuestionnaireCreate, 
    request: Request, 
    db: Session = Depends(get_db)
):
    """
    Create a new questionnaire with its first version.
    """
    created_by_id = None
    if hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
    return create_questionnaire(db, questionnaire_in, created_by_id=created_by_id)

@router.put(
    "/questionnaires/{questionnaire_id}", 
    response_model=QuestionnaireOut,
    summary="Update an existing questionnaire",
    tags=["questionnaires"],
    description="""
    Update a questionnaire's base properties (not version-specific details).
    This only updates the questionnaire record itself, not its versions.
    Use the version-specific endpoints to update version details.
    """
)
def update_existing_questionnaire(
    questionnaire_id: int = Path(..., ge=1, description="The ID of the questionnaire to update"),
    questionnaire_in: QuestionnaireUpdate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Update a questionnaire's base properties (not version-specific details).
    """
    updated_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        updated_by_id = request.state.user.get("id")
        
    questionnaire = update_questionnaire(db, questionnaire_id, questionnaire_in, updated_by_id=updated_by_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")
    return questionnaire


# Questionnaire Version endpoints
@router.get(
    "/questionnaires/{questionnaire_id}/versions", 
    response_model=List[QuestionnaireVersionOut],
    summary="Get all versions of a specific questionnaire",
    tags=["questionnaire versions"],
    description="Get all versions of a specific questionnaire, with sorting and pagination options."
)
def list_questionnaire_versions(
    questionnaire_id: int = Path(..., ge=1, description="The ID of the questionnaire"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Number of records to return"),
    sort_by: str = Query("version", description="Field to sort by"),
    sort_desc: bool = Query(True, description="Sort in descending order"),
    db: Session = Depends(get_db)
):
    """
    Get all versions of a specific questionnaire.
    """
    # First check if questionnaire exists
    questionnaire = get_questionnaire(db, questionnaire_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")
        
    versions, _ = get_questionnaire_versions(
        db,
        questionnaire_id,
        skip=skip,
        limit=limit,
        sort_by=sort_by,
        sort_desc=sort_desc
    )
    
    return [QuestionnaireVersionOut.model_validate(v) for v in versions]

@router.get(
    "/questionnaires/versions/{version_id}", 
    response_model=QuestionnaireVersionOut,
    summary="Get a specific questionnaire version",
    tags=["questionnaire versions"],
    description="Get detailed information about a specific questionnaire version by its ID."
)
def read_questionnaire_version(
    version_id: int = Path(..., ge=1, description="The ID of the questionnaire version to get"),
    db: Session = Depends(get_db)
):
    """
    Get a specific questionnaire version by its ID.
    """
    version = get_questionnaire_version(db, version_id)
    if not version:
        raise HTTPException(status_code=404, detail="Questionnaire version not found")
    return version

@router.post(
    "/questionnaires/{questionnaire_id}/versions", 
    response_model=QuestionnaireVersionOut, 
    status_code=status.HTTP_201_CREATED,
    summary="Create a new version for a questionnaire",
    tags=["questionnaire versions"],
    description="""
    Create a new version for an existing questionnaire.
    If status is set to Active, all other versions of this questionnaire will be set to Archived.
    Only one version can be active at a time per questionnaire.
    """
)
def create_new_questionnaire_version(
    questionnaire_id: int = Path(..., ge=1, description="The ID of the questionnaire"),
    version_in: QuestionnaireVersionCreate = ...,
    request: Request = None,
    db: Session = Depends(get_db)
):
    """
    Create a new version for an existing questionnaire.
    """
    # First check if questionnaire exists
    questionnaire = get_questionnaire(db, questionnaire_id)
    if not questionnaire:
        raise HTTPException(status_code=404, detail="Questionnaire not found")
        
    created_by_id = None
    if request and hasattr(request.state, "user") and request.state.user:
        created_by_id = request.state.user.get("id")
        
    return create_questionnaire_version(db, questionnaire_id, version_in, created_by_id=created_by_id)

@router.put(
    "/questionnaires/versions/{version_id}", 
    response_model=QuestionnaireVersionOut,
    summary="Update an existing questionnaire version",
    tags=["questionnaire versions"],
    description="""
    Update an existing questionnaire version.
    If status is changed to Active, all other versions of this questionnaire will be set to Archived.
    Only one version can be active at a time per questionnaire.
    """
)
def update_existing_questionnaire_version(
    version_id: int = Path(..., ge=1, description="The ID of the questionnaire version to update"),
    version_in: QuestionnaireVersionUpdate = ...,
    db: Session = Depends(get_db)
):
    """
    Update an existing questionnaire version.
    """
    version = update_questionnaire_version(db, version_id, version_in)
    if not version:
        raise HTTPException(status_code=404, detail="Questionnaire version not found")
    return version

from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse, JSONResponse
from app.services.s3_service import upload_file_to_s3, get_file_stream_from_s3, get_s3_static_url
import botocore

router = APIRouter()

@router.post("/files/upload", tags=["files"], summary="Upload file to S3 and get static URL")
async def upload_file(file: UploadFile = File(...)):
    key = file.filename
    try:
        url = upload_file_to_s3(file.file, key, content_type=file.content_type)
        return {"key": key, "static_url": url}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

import mimetypes

@router.get("/files/{filename}", tags=["files"], summary="Serve static file from S3")
async def get_file(filename: str):
    try:
        file_stream = get_file_stream_from_s3(filename)
        media_type, _ = mimetypes.guess_type(filename)
        if not media_type:
            media_type = "application/octet-stream"
        return StreamingResponse(file_stream, media_type=media_type)
    except botocore.exceptions.ClientError as e:
        if e.response['Error']['Code'] == "NoSuchKey":
            raise HTTPException(status_code=404, detail="File not found")
        raise HTTPException(status_code=500, detail="Failed to fetch file")

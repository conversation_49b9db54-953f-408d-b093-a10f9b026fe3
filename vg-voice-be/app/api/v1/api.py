from fastapi import APIRouter
from app.api.v1.endpoints import projects, personas, questionnaires, prompts, calls, dashboard, future, exotel, users, files

api_router = APIRouter()

api_router.include_router(projects.router)
api_router.include_router(personas.router)
api_router.include_router(questionnaires.router)
api_router.include_router(prompts.router)
api_router.include_router(calls.router)
api_router.include_router(dashboard.router)
api_router.include_router(future.router)
api_router.include_router(exotel.router)
api_router.include_router(users.router)
api_router.include_router(files.router)

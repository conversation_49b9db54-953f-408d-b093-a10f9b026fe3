import httpx
from typing import Any, Dict, Optional
from app.config.envconfig import settings
from fastapi import APIRouter, HTTPException, Body, status
from app.utils.logger import get_logger

logger = get_logger("llm_service")

router = APIRouter()

class LLMService:
    def __init__(self):
        self.base_url = settings.llm_service_host
        self.api_key = settings.llm_service_api_key
        self.headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }

    async def get_transcript(self, audio_url: str) -> str:
        payload = {
            "audio_url": audio_url,
            "language_code": "hi",
            "post_process_transcript_with_llm": False,
            "llm_model": "gpt-4",
            "output_format": "json"
        }
        url = f"{self.base_url}/activity/transcribe"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=self.headers, timeout=120.0)
                response.raise_for_status()
                return response.json()["transcription"]
        except httpx.ReadTimeout:
            logger.error(f"Transcript service timed out for audio_url: {audio_url}", exc_info=True)
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"LLMService transcript HTTP error: {e.response.status_code} {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"LLMService transcript error: {str(e)}")
            return None

    async def process_from_prompt(self, input_data: dict, prompt_template: str) -> str:
        payload = {
            "input_data": input_data,
            "prompt_template": prompt_template,
            "llm_model": "gpt-4",
            "output_format": "json"
        }
        url = f"{self.base_url}/activity/process_from_prompt"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=self.headers, timeout=120.0)
                response.raise_for_status()
                return response.json()["response"]
        except httpx.ReadTimeout:
            logger.error(f"LLMService process_from_prompt timeout", exc_info=True)
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"LLMService process_from_prompt HTTP error: {e.response.status_code} {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"LLMService transcript error: {str(e)}")
            raise

    async def post_process_intelligence(self, input_data: dict, prompt_template: str, meta_data: dict, audio_url: str, generate_transcript: bool = False) -> dict:
        payload = {
            "llm_model": "gpt-4",
            "input_data": input_data,
            "meta_data": meta_data,
            "generate_transcript": generate_transcript,
            "audio_url": audio_url,
            "prompt_template": prompt_template
        }
        url = f"{self.base_url}/activity/post_process_intelligence"
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=self.headers, timeout=120.0)
                response.raise_for_status()
                return response.json()["metrics"]
        except httpx.ReadTimeout:
            logger.error("LLMService post_process_intelligence read timeout")
            raise
        except httpx.HTTPStatusError as e:
            logger.error(f"LLMService post_process_intelligence HTTP error: {e.response.status_code} {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"LLMService post_process_intelligence error: {str(e)}")
            raise


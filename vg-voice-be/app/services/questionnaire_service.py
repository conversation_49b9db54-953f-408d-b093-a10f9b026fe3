from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.questionnaire import Questionnaire, QuestionnaireStatus
from app.schemas.questionnaire import QuestionnaireCreate, QuestionnaireUpdate, QuestionnaireVersionCreate
from app.services.questionnaire_version_service import (
    create_questionnaire_version, 
    get_latest_questionnaire_version,
    get_active_questionnaire_version
)
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

def get_questionnaire(db: Session, questionnaire_id: int) -> Optional[Questionnaire]:
    """Get a specific questionnaire by ID with its active version."""
    questionnaire = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if questionnaire:
        # Add the active version to the questionnaire (fallback to latest if no active version)
        questionnaire.current_version = get_active_questionnaire_version(db, questionnaire.id) or get_latest_questionnaire_version(db, questionnaire.id)
    return questionnaire

def get_questionnaires(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    filters: Dict[str, Any] = None,
    search: str = None,
    sort_by: str = "created_at",
    sort_desc: bool = True
) -> <PERSON><PERSON>[List[Questionnaire], int]:
    """
    Get questionnaires with filtering, search and sorting.
    Returns a tuple of (questionnaires, total_count)
    """
    query = db.query(Questionnaire)
    
    # Apply filters
    if filters:
        if filters.get("status"):
            query = query.filter(Questionnaire.status == filters["status"])
        if filters.get("project_id"):
            query = query.filter(Questionnaire.project_id == filters["project_id"])
        if filters.get("created_by_id"):
            query = query.filter(Questionnaire.created_by_id == filters["created_by_id"])
    
    # Apply search
    if search:
        search_term = f"%{search}%"
        query = query.join(
            QuestionnaireVersion,
            Questionnaire.id == QuestionnaireVersion.questionnaire_id
        ).filter(
            or_(
                QuestionnaireVersion.question_text.ilike(search_term),
                QuestionnaireVersion.question_type.ilike(search_term)
            )
        ).distinct()
  
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(Questionnaire, sort_by):
        if sort_desc:
            query = query.order_by(getattr(Questionnaire, sort_by).desc())
        else:
            query = query.order_by(getattr(Questionnaire, sort_by).asc())
    
    # Apply pagination and fetch results
    questionnaires = query.offset(skip).limit(limit).all()
    
    # For each questionnaire, fetch the active version (fallback to latest if no active version)
    for questionnaire in questionnaires:
        questionnaire.current_version = get_active_questionnaire_version(db, questionnaire.id) or get_latest_questionnaire_version(db, questionnaire.id)
    
    return questionnaires, total

def create_questionnaire(db: Session, questionnaire_in: QuestionnaireCreate, created_by_id: int = None) -> Questionnaire:
    """Create a new questionnaire with its first version."""
    now = datetime.utcnow()
    
    # Create the new questionnaire
    questionnaire_obj = Questionnaire(
        project_id=questionnaire_in.project_id,
        status=questionnaire_in.status,
        created_by_id=created_by_id,
        updated_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(questionnaire_obj)
    db.flush()  # Flush to get the ID before creating version
    
    # Create the initial version (1.0)
    version_data = QuestionnaireVersionCreate(
        question_text=questionnaire_in.question_text,
        question_type=questionnaire_in.question_type,
        options=questionnaire_in.options,
        order_no=questionnaire_in.order_no,
        status=QuestionnaireStatus.ACTIVE,  # Always create the first version as active
        change_notes="Initial version"
    )
    
    version_obj = create_questionnaire_version(
        db,
        questionnaire_obj.id,
        version_data,
        created_by_id
    )
    
    # Add the current version to the questionnaire object
    questionnaire_obj.current_version = version_obj
    
    db.commit()
    db.refresh(questionnaire_obj)
    
    return questionnaire_obj

def update_questionnaire(db: Session, questionnaire_id: int, questionnaire_in: QuestionnaireUpdate, updated_by_id: int = None) -> Optional[Questionnaire]:
    """Update an existing questionnaire."""
    db_obj = db.query(Questionnaire).filter(Questionnaire.id == questionnaire_id).first()
    if not db_obj:
        return None
        
    now = datetime.utcnow()
    update_data = questionnaire_in.dict(exclude_unset=True)
    
    # Update the questionnaire object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
        
    db_obj.updated_at = now
    db_obj.updated_by_id = updated_by_id
    
    db.commit()
    db.refresh(db_obj)
    
    # Add the active version to the questionnaire object (fallback to latest if no active version)
    db_obj.current_version = get_active_questionnaire_version(db, db_obj.id) or get_latest_questionnaire_version(db, db_obj.id)
    
    return db_obj

def get_questionnaire_stats(db: Session) -> Dict[str, Any]:
    """Get questionnaire statistics."""
    total = db.query(func.count(Questionnaire.id)).scalar()
    
    status_counts = db.query(
        Questionnaire.status, 
        func.count(Questionnaire.id)
    ).group_by(Questionnaire.status).all()
    
    project_counts = db.query(
        Questionnaire.project_id, 
        func.count(Questionnaire.id)
    ).group_by(Questionnaire.project_id).all()
    
    return {
        "total": total,
        "by_status": {status: count for status, count in status_counts},
        "by_project": {str(project_id): count for project_id, count in project_counts}
    }
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.prompt import Prompt, PromptVersion, PromptStatus, PromptType, SUMMARY_PROMPT_QUESTIONS_KEY
from app.schemas.prompt import PromptVersionCreate, PromptVersionUpdate
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import re

def get_prompt_version(db: Session, version_id: int) -> Optional[PromptVersion]:
    """Get a specific prompt version by ID."""
    version = db.query(PromptVersion).filter(PromptVersion.id == version_id).first()
    if version:
        # Extract variables from prompt text
        version.variables = get_variables_from_prompt_text(version.prompt_text)
    return version

def get_prompt_versions(
    db: Session,
    prompt_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = "version",
    sort_desc: bool = True
) -> <PERSON>ple[List[PromptVersion], int]:
    """Get all versions of a specific prompt."""
    query = db.query(PromptVersion).filter(PromptVersion.prompt_id == prompt_id)
    
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(PromptVersion, sort_by):
        if sort_desc:
            query = query.order_by(getattr(PromptVersion, sort_by).desc())
        else:
            query = query.order_by(getattr(PromptVersion, sort_by).asc())
    
    # Apply pagination
    versions = query.offset(skip).limit(limit).all()
    
    # Extract variables for each version
    for version in versions:
        version.variables = get_variables_from_prompt_text(version.prompt_text)
    
    return versions, total

def get_latest_prompt_version(db: Session, prompt_id: int) -> Optional[PromptVersion]:
    """Get the latest version of a prompt."""
    version = db.query(PromptVersion).filter(
        PromptVersion.prompt_id == prompt_id
    ).order_by(PromptVersion.version.desc()).first()
    
    if version:
        # Extract variables from prompt text
        version.variables = get_variables_from_prompt_text(version.prompt_text)
    
    return version

def get_active_prompt_version(db: Session, prompt_id: int) -> Optional[PromptVersion]:
    """Get the active version of a prompt."""
    version = db.query(PromptVersion).filter(
        PromptVersion.prompt_id == prompt_id,
        PromptVersion.status == PromptStatus.ACTIVE
    ).first()
    
    if version:
        # Extract variables from prompt text
        version.variables = get_variables_from_prompt_text(version.prompt_text)
    
    return version

from app.models.prompt import SUMMARY_PROMPT_QUESTIONS_KEY

def _validate_summary_prompt_text(prompt_type, prompt_text):
    """
    If prompt_type is SUMMARY, ensure prompt_text contains SUMMARY_PROMPT_QUESTIONS_KEY.
    Raise HTTPException if not.
    """
    if prompt_type and prompt_type == PromptType.SUMMARY:
        if not prompt_text or SUMMARY_PROMPT_QUESTIONS_KEY not in prompt_text:
            raise HTTPException(
                status_code=422,
                detail=f"For SUMMARY prompt_type, prompt_text must contain '{SUMMARY_PROMPT_QUESTIONS_KEY}'."
            )

def create_prompt_version(
    db: Session, 
    prompt_id: int, 
    version_data: PromptVersionCreate,
    created_by_id: int = None
) -> PromptVersion:
    """
    Create a new version for a prompt.
    If the new version is marked as active, ensure all other versions for this prompt
    are set to inactive.
    """
    now = datetime.utcnow()

    # Validation: If prompt type is SUMMARY, prompt_text must contain SUMMARY_PROMPT_QUESTIONS_KEY
    prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
    _validate_summary_prompt_text(prompt.type, version_data.prompt_text)

    # Find the latest version number and increment
    latest_version = get_latest_prompt_version(db, prompt_id)
    
    if latest_version:
        # Increment the version number
        major, minor = map(int, latest_version.version.split('.'))
        new_version = f"{major}.{minor + 1}"
    else:
        # First version
        new_version = "1.0"
    
    # Check if this version is set to active
    if version_data.status == PromptStatus.ACTIVE:
        # Set all other versions of this prompt to archived
        active_versions = db.query(PromptVersion).filter(
            PromptVersion.prompt_id == prompt_id,
            PromptVersion.status == PromptStatus.ACTIVE
        ).all()
        
        for version in active_versions:
            version.status = PromptStatus.ARCHIVED
            version.updated_at = now
    
    # Create new version
    db_obj = PromptVersion(
        prompt_id=prompt_id,
        prompt_text=version_data.prompt_text,
        model=version_data.model,
        version=new_version,
        status=version_data.status,
        change_notes=version_data.change_notes,
        created_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    
    # Extract variables from prompt text
    db_obj.variables = get_variables_from_prompt_text(db_obj.prompt_text)
    
    return db_obj

def update_prompt_version(
    db: Session, 
    version_id: int, 
    version_data: PromptVersionUpdate
) -> Optional[PromptVersion]:

    # Validation: If prompt type is SUMMARY, prompt_text must contain SUMMARY_PROMPT_QUESTIONS_KEY
    prompt_version_obj = db.query(PromptVersion).filter(PromptVersion.id == version_id).first()
    prompt = db.query(Prompt).filter(Prompt.id == prompt_version_obj.prompt_id).first()
    _validate_summary_prompt_text(prompt.type, version_data.prompt_text)

    """
    Update an existing prompt version.
    If status is changed to Active, ensure all other versions of this prompt are set to inactive.
    """
    db_obj = db.query(PromptVersion).filter(PromptVersion.id == version_id).first()
    if not db_obj:
        return None
    
    now = datetime.utcnow()
    update_data = version_data.dict(exclude_unset=True)
    
    # Check if we're activating this version
    if update_data.get("status") == PromptStatus.ACTIVE:
        # Set all other versions of this prompt to archived
        active_versions = db.query(PromptVersion).filter(
            PromptVersion.prompt_id == db_obj.prompt_id,
            PromptVersion.status == PromptStatus.ACTIVE,
            PromptVersion.id != version_id
        ).all()
        
        for version in active_versions:
            version.status = PromptStatus.ARCHIVED
            version.updated_at = now
    
    # Update the version object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db_obj.updated_at = now
    db.commit()
    db.refresh(db_obj)
    
    # Extract variables from prompt text (only if prompt_text was updated or not already set)
    db_obj.variables = get_variables_from_prompt_text(db_obj.prompt_text)
    
    return db_obj

def get_variables_from_prompt_text(prompt_text: str) -> List[str]:
    """
    Extract variables from the prompt text. 
    Variables are identified by curly braces, e.g., {variable_name}
    """
    if not prompt_text:
        return []
    return re.findall(r"\{(.*?)\}", prompt_text)
from sqlalchemy.orm import Session
from typing import Optional
import requests
import json
from datetime import datetime, timedelta

from app.models.user import User
from app.schemas.user import User<PERSON><PERSON>, UserLogin
from passlib.context import CryptContext
from jose import JWTError, jwt
from app.config.envconfig import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Password hashing setup
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = settings.auth_jwt_secret
ALGORITHM = settings.auth_jwt_algorithm
ACCESS_TOKEN_EXPIRE_MINUTES = settings.auth_jwt_expire_minutes


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)


def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        token_data = payload
    except JWTError:
        raise Exception("Token is invalid or expired")
    return token_data
  
def validate_token(db: Session, token: str):
    response = verify_access_token(token)
    if not response:
        return Exception("Token is invalid or expired")
    user_id = response.get("sub")
    if not user_id:
        return Exception("Token is invalid or expired")
      
    user = get_user_by_id(db, user_id)
    if not user:
        return Exception("User not found")
    if not user.is_active:
        return Exception("User is inactive")
    
    return {
        "id": user.id,
        "email": user.email,
        "username": user.username,
        "is_active": user.is_active
    }


def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()


def create_user(db: Session, user: UserCreate):
    hashed_password = get_password_hash(user.password)
    db_user = User(
        username=user.username,
        email=user.email,
        password=hashed_password,
        first_name=user.first_name,
        last_name=user.last_name,
        is_active=True
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, email: str, password: str):
    user = get_user_by_email(db, email)
    if not user:
        return False
    if not user.password or not verify_password(password, user.password):
        return False
    return user


def verify_google_token(id_token: str):
    """Verify the Google ID token and ensure the user belongs to vegrow.in domain"""
    try:
        url = f"https://www.googleapis.com/oauth2/v3/tokeninfo?id_token={id_token}"
        headers = {}
        response = requests.post(url, headers=headers)
        
        if response.status_code == 200:
            parsed_response = response.json()
            if parsed_response.get("hd") == "vegrow.in":
                return parsed_response
        return None
    except Exception as e:
        logger.error(f"Error verifying Google token: {e}")
        return None
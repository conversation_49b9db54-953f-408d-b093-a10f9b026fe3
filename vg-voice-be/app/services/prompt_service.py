from app.models.persona import Persona
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.prompt import Prompt, PromptStatus, PromptVersion
from app.schemas.prompt import PromptCreate, PromptUpdate, PromptVersionCreate
from app.services.prompt_version_service import (
    create_prompt_version, 
    get_latest_prompt_version,
    get_active_prompt_version
)
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

def get_prompt(db: Session, prompt_id: int) -> Optional[Prompt]:
    """Get a specific prompt by ID with its active version."""
    prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
    if prompt:
        # Add the active version to the prompt (fallback to latest if no active version)
        version = get_active_prompt_version(db, prompt.id) or get_latest_prompt_version(db, prompt.id)
        if version:
            # Extract variables from prompt text and set them in the version object
            version.variables = get_variables_from_prompt_text(version.prompt_text)
        prompt.current_version = version
    return prompt

def get_prompts(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    filters: Dict[str, Any] = None,
    search: str = None,
    sort_by: str = "created_at",
    sort_desc: bool = True
) -> Tuple[List[Prompt], int]:
    """
    Get prompts with filtering, search and sorting.
    Returns a tuple of (prompts, total_count)
    """
    query = db.query(Prompt)
    
    # Apply filters
    if filters:
        if filters.get("status"):
            query = query.filter(Prompt.status == filters["status"])
        if filters.get("persona_id"):
            query = query.filter(Prompt.persona_id == filters["persona_id"])
        if filters.get("type"):
            query = query.filter(Prompt.type == filters["type"])
        if filters.get("created_by_id"):
            query = query.filter(Prompt.created_by_id == filters["created_by_id"])
        if filters.get("project_id"):
            query = query.join(
                Persona,
                Prompt.persona_id == Persona.id
            ).filter(Persona.project_id == filters["project_id"])
    
    # Apply search
    if search:
        search_term = f"%{search}%"
        query = query.join(
            PromptVersion,
            Prompt.id == PromptVersion.prompt_id
        ).filter(
            or_(
                Prompt.type.ilike(search_term),
                PromptVersion.prompt_text.ilike(search_term),
                PromptVersion.model.ilike(search_term)
            )
        ).distinct()
  
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(Prompt, sort_by):
        if sort_desc:
            query = query.order_by(getattr(Prompt, sort_by).desc())
        else:
            query = query.order_by(getattr(Prompt, sort_by).asc())
    
    # Apply pagination and fetch results
    prompts = query.offset(skip).limit(limit).all()
    
    # For each prompt, fetch the active version (fallback to latest if no active version)
    for prompt in prompts:
        version = get_active_prompt_version(db, prompt.id) or get_latest_prompt_version(db, prompt.id)
        if version:
            # Extract variables from prompt text and set them in the version object
            version.variables = get_variables_from_prompt_text(version.prompt_text)
        prompt.current_version = version
    
    return prompts, total

def create_prompt(db: Session, prompt_in: PromptCreate, created_by_id: int = None) -> Prompt:
    """Create a new prompt with its first version."""
    now = datetime.utcnow()
    
    # Create the new prompt
    prompt_obj = Prompt(
        persona_id=prompt_in.persona_id,
        type=prompt_in.type,
        status=prompt_in.status,
        created_by_id=created_by_id,
        updated_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(prompt_obj)
    db.flush()  # Flush to get the ID before creating version
    
    # Create the initial version (1.0)
    version_data = PromptVersionCreate(
        prompt_text=prompt_in.prompt_text,
        model=prompt_in.model,
        status=PromptStatus.ACTIVE,  # Always create the first version as active
        change_notes="Initial version"
    )
    
    version_obj = create_prompt_version(
        db,
        prompt_obj.id,
        version_data,
        created_by_id
    )
    
    # Extract variables from prompt text and set them in the version object
    version_obj.variables = get_variables_from_prompt_text(version_obj.prompt_text)
    
    # Add the current version to the prompt object
    prompt_obj.current_version = version_obj
    
    db.commit()
    db.refresh(prompt_obj)
    
    return prompt_obj

def update_prompt(db: Session, prompt_id: int, prompt_in: PromptUpdate, updated_by_id: int = None) -> Optional[Prompt]:
    """Update an existing prompt."""
    db_obj = db.query(Prompt).filter(Prompt.id == prompt_id).first()
    if not db_obj:
        return None
        
    now = datetime.utcnow()
    update_data = prompt_in.dict(exclude_unset=True)
    
    # Update the prompt object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
        
    db_obj.updated_at = now
    db_obj.updated_by_id = updated_by_id
    
    db.commit()
    db.refresh(db_obj)
    
    # Add the active version to the prompt object (fallback to latest if no active version)
    version = get_active_prompt_version(db, db_obj.id) or get_latest_prompt_version(db, db_obj.id)
    if version:
        # Extract variables from prompt text and set them in the version object
        version.variables = get_variables_from_prompt_text(version.prompt_text)
    db_obj.current_version = version
    
    return db_obj

def get_prompt_stats(db: Session) -> Dict[str, Any]:
    """Get prompt statistics."""
    total = db.query(func.count(Prompt.id)).scalar()
    
    status_counts = db.query(
        Prompt.status, 
        func.count(Prompt.id)
    ).group_by(Prompt.status).all()
    
    persona_counts = db.query(
        Prompt.persona_id, 
        func.count(Prompt.id)
    ).group_by(Prompt.persona_id).all()
    
    return {
        "total": total,
        "by_status": {status: count for status, count in status_counts},
        "by_persona": {str(persona_id): count for persona_id, count in persona_counts}
    }
    
def get_variables_from_prompt_text(prompt_text: str) -> List[str]:
    """
    Extract variables from the prompt text.
    This is a placeholder function and should be implemented based on the actual variable extraction logic.
    """
    import re
    return re.findall(r"\{(.*?)\}", prompt_text)

def replace_variables_in_prompt_text(prompt_text: str, variables: Dict[str, str]) -> str:
    """
    Replace variables in the prompt text with their values.
    """
    for var, value in variables.items():
        prompt_text = prompt_text.replace(f"{{{var}}}", value)
    return prompt_text
import boto3
import requests
from io import BytesIO
from botocore.exceptions import ClientError
from app.config.envconfig import settings
from typing import IO
from app.utils.logger import get_logger

logger = get_logger("s3_service")

def get_s3_client():
    return boto3.client(
        "s3",
        aws_access_key_id=settings.aws_access_key_id,
        aws_secret_access_key=settings.aws_secret_access_key,
        region_name=settings.aws_s3_region,
    )

def upload_file_to_s3(file_obj: IO, key: str, content_type: str = "application/octet-stream") -> str:
    """
    Uploads a file-like object to S3 and returns the static S3 URL (private bucket, not public).
    """
    s3_client = get_s3_client()
    try:
        s3_client.upload_fileobj(
            Fileobj=file_obj,
            Bucket=settings.aws_s3_bucket,
            Key=key,
            ExtraArgs={"ContentType": content_type, "ACL": "private"}
        )
        logger.info(f"File uploaded to S3: {key}")
        return get_s3_static_url(key)
    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:
        logger.error(f"Failed to upload file to S3: {e}")
        raise RuntimeError(f"Failed to upload file to S3: {e}")


def get_s3_static_url(key: str) -> str:
    """
    Returns the static S3 object URL (does not expire, but requires AWS auth if bucket is private).
    """
    return f"{settings.vg_voice_be_host}/voice/api/v1/files/{key}"

def upload_url_to_s3(file_name: str, url: str, auth: tuple = None) -> str:
    """
    Downloads a file from the given URL and uploads it to S3.
    Returns the S3 static URL.
    """
    resp = requests.get(url, auth=auth)
    resp.raise_for_status()
    file_obj = BytesIO(resp.content)
    return upload_file_to_s3(file_obj, file_name)


def get_file_stream_from_s3(key: str):
    s3 = get_s3_client()
    return s3.get_object(Bucket=settings.aws_s3_bucket, Key=key)['Body']

import httpx
from app.config.envconfig import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

async def get_current_user_profile(token: str) -> dict:
    """
    Calls the Velynk user service to validate the token and fetch the current user profile.
    Returns user info dict if valid, raises httpx.HTTPStatusError otherwise.
    """
    url = f"{settings.velynk_service_host}/users/current_user_profile"
    headers = {"Authorization": token, "Accept": "application/json"}
    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, timeout=5.0)
        response.raise_for_status()
        return response.json()

from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.persona import PersonaVersion, PersonaStatus
from app.schemas.persona import PersonaVersionCreate, PersonaVersionUpdate
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

def get_persona_version(db: Session, version_id: int) -> Optional[PersonaVersion]:
    """Get a specific persona version by ID."""
    return db.query(PersonaVersion).filter(PersonaVersion.id == version_id).first()

def get_persona_versions(
    db: Session,
    persona_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = "version",
    sort_desc: bool = True
) -> Tuple[List[PersonaVersion], int]:
    """Get all versions of a specific persona."""
    query = db.query(PersonaVersion).filter(PersonaVersion.persona_id == persona_id)
    
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(PersonaVersion, sort_by):
        if sort_desc:
            query = query.order_by(getattr(PersonaVersion, sort_by).desc())
        else:
            query = query.order_by(getattr(PersonaVersion, sort_by).asc())
    
    # Apply pagination
    versions = query.offset(skip).limit(limit).all()
    
    return versions, total

def get_latest_persona_version(db: Session, persona_id: int) -> Optional[PersonaVersion]:
    """Get the latest version of a persona."""
    return db.query(PersonaVersion).filter(
        PersonaVersion.persona_id == persona_id
    ).order_by(PersonaVersion.version.desc()).first()

def get_active_persona_version(db: Session, persona_id: int) -> Optional[PersonaVersion]:
    """Get the active version of a persona."""
    return db.query(PersonaVersion).filter(
        PersonaVersion.persona_id == persona_id,
        PersonaVersion.status == PersonaStatus.ACTIVE
    ).first()

def create_persona_version(
    db: Session, 
    persona_id: int, 
    version_data: PersonaVersionCreate,
    created_by_id: int = None
) -> PersonaVersion:
    """
    Create a new version for a persona.
    If the new version is marked as active, ensure all other versions for this persona
    are set to inactive.
    """
    now = datetime.utcnow()
    
    # Find the latest version number and increment
    latest_version = get_latest_persona_version(db, persona_id)
    
    if latest_version:
        # Increment the version number
        major, minor = map(int, latest_version.version.split('.'))
        new_version = f"{major}.{minor + 1}"
    else:
        # First version
        new_version = "1.0"
    
    # Check if this version is set to active
    if version_data.status == PersonaStatus.ACTIVE:
        # Set all other versions of this persona to archived
        active_versions = db.query(PersonaVersion).filter(
            PersonaVersion.persona_id == persona_id,
            PersonaVersion.status == PersonaStatus.ACTIVE
        ).all()
        
        for version in active_versions:
            version.status = PersonaStatus.ARCHIVED
            version.updated_at = now
    
    # Create new version
    db_obj = PersonaVersion(
        persona_id=persona_id,
        voice_type=version_data.voice_type,
        engine=version_data.engine,
        language=version_data.language,
        from_number=version_data.from_number,
        version=new_version,
        status=version_data.status,
        change_notes=version_data.change_notes,
        created_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    
    return db_obj

def update_persona_version(
    db: Session, 
    version_id: int, 
    version_data: PersonaVersionUpdate
) -> Optional[PersonaVersion]:
    """
    Update an existing persona version.
    If status is changed to Active, ensure all other versions of this persona are set to inactive.
    """
    db_obj = db.query(PersonaVersion).filter(PersonaVersion.id == version_id).first()
    if not db_obj:
        return None
    
    now = datetime.utcnow()
    update_data = version_data.dict(exclude_unset=True)
    
    # Check if we're activating this version
    if update_data.get("status") == PersonaStatus.ACTIVE:
        # Set all other versions of this persona to archived
        active_versions = db.query(PersonaVersion).filter(
            PersonaVersion.persona_id == db_obj.persona_id,
            PersonaVersion.status == PersonaStatus.ACTIVE,
            PersonaVersion.id != version_id
        ).all()
        
        for version in active_versions:
            version.status = PersonaStatus.ARCHIVED
            version.updated_at = now
    
    # Update the version object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db_obj.updated_at = now
    db.commit()
    db.refresh(db_obj)
    
    return db_obj
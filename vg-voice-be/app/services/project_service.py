from sqlalchemy.orm import Session
from sqlalchemy import or_, func
from app.models.project import Project, ProjectStatus
from app.schemas.project import ProjectCreate, ProjectUpdate
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

def get_project(db: Session, project_id: int) -> Optional[Project]:
    """Get a specific project by ID."""
    query = db.query(Project).filter(Project.id == project_id)
        
    return query.first()

def get_projects(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    filters: Dict[str, Any] = None,
    search: str = None,
    sort_by: str = "created_at",
    sort_desc: bool = True
) -> Tuple[List[Project], int]:
    """
    Get projects with filtering, search and sorting.
    Returns a tuple of (projects, total_count)
    """
    query = db.query(Project)
    
    # Apply filters
    if filters:
        if filters.get("status"):
            query = query.filter(Project.status == filters["status"])
        if filters.get("language"):
            query = query.filter(Project.language == filters["language"])
        if filters.get("region"):
            query = query.filter(Project.region == filters["region"])
        if filters.get("created_by_id"):
            query = query.filter(Project.created_by_id == filters["created_by_id"])
        
    # Apply search
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Project.title.ilike(search_term),
                Project.language.ilike(search_term),
                Project.region.ilike(search_term)
            )
        )
    
    # Don't include deleted projects by default
    query = query.filter(Project.status != ProjectStatus.ARCHIVED)
    
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if sort_desc:
        query = query.order_by(getattr(Project, sort_by).desc())
    else:
        query = query.order_by(getattr(Project, sort_by).asc())
    
    # Apply pagination
    projects = query.offset(skip).limit(limit).all()
    
    return projects, total

def create_project(db: Session, project_in: ProjectCreate, created_by_id: str = None) -> Project:
    """Create a new project."""
    now = datetime.utcnow()
    db_obj = Project(
        title=project_in.title,
        language=project_in.language,
        region=project_in.region,
        status="Active",
        created_at=now,
        updated_at=now,
        created_by_id=created_by_id
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    
    return db_obj

def update_project(db: Session, project_id: int, project_in: ProjectUpdate) -> Optional[Project]:
    """Update an existing project."""
    db_obj = db.query(Project).filter(Project.id == project_id).first()
    if not db_obj:
        return None
        
    update_data = project_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_obj, field, value)
        
    db_obj.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_obj)
    
    return db_obj

def delete_project(db: Session, project_id: int) -> bool:
    """Delete a project (soft delete by default)."""
    db_obj = db.query(Project).filter(Project.id == project_id).first()
    if not db_obj:
        return False
    db_obj.status = ProjectStatus.INACTIVE
    db_obj.updated_at = datetime.utcnow()
        
    db.commit()
    
    return True

def get_project_stats(db: Session) -> Dict[str, Any]:
    """Get project statistics."""
    total = db.query(func.count(Project.id)).scalar()
    
    status_counts = db.query(
        Project.status, 
        func.count(Project.id)
    ).group_by(Project.status).all()
        
    return {
        "total": total,
        "by_status": {status: count for status, count in status_counts}
    }

from motor.motor_asyncio import AsyncIOMotorClient
from app.config.envconfig import settings

class MongoService:
    def __init__(self):
        self.client = AsyncIOMotorClient(settings.mongo_uri)
        self.db = self.client[settings.mongo_db]

    def get_collection(self, collection_name: str):
        return self.db[collection_name]

    @staticmethod
    async def find_or_create_record(collection, query: dict, base_doc: dict):
        doc = await collection.find_one(query)
        if not doc:
            await collection.insert_one(base_doc)
        return await collection.find_one(query)

    @staticmethod
    async def update_record(collection, query: dict, update_fields: dict, updated_at: str):
        update_fields["updated_at"] = updated_at
        await collection.update_one(
            query,
            {"$set": update_fields},
            upsert=True
        )

    async def close(self):
        await self.client.close()

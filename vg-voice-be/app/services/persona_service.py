from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.persona import Persona, PersonaVersion, PersonaStatus
from app.schemas.persona import PersonaCreate, PersonaUpdate, PersonaVersionCreate
from app.services.persona_version_service import (
    create_persona_version, 
    get_latest_persona_version,
    get_active_persona_version
)
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from app.utils.logger import get_logger

logger = get_logger(__name__)

def get_persona(db: Session, persona_id: int) -> Optional[Persona]:
    """Get a specific persona by ID with its active version."""
    persona = db.query(Persona).filter(Persona.id == persona_id).first()
    if persona:
        # Add the active version to the persona (fallback to latest if no active version)
        persona.current_version = get_active_persona_version(db, persona.id) or get_latest_persona_version(db, persona.id)
    return persona

def get_personas(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    filters: Dict[str, Any] = None,
    search: str = None,
    sort_by: str = "created_at",
    sort_desc: bool = True
) -> Tuple[List[Persona], int]:
    """
    Get personas with filtering, search and sorting.
    Returns a tuple of (personas, total_count)
    """
    query = db.query(Persona)
    
    # Apply filters
    if filters:
        if filters.get("status"):
            query = query.filter(Persona.status == filters["status"])
        if filters.get("project_id"):
            query = query.filter(Persona.project_id == filters["project_id"])
        if filters.get("created_by_id"):
            query = query.filter(Persona.created_by_id == filters["created_by_id"])
        
        # For version-specific filters, we need to join with PersonaVersion
        version_filters = ["language", "voice_type", "engine"]
        if any(filters.get(f) for f in version_filters):
            # We'll join with active versions, or if no active version exists, the latest version
            query = query.join(
                PersonaVersion,
                Persona.id == PersonaVersion.persona_id
            )
            
            # Prioritize active versions
            if filters.get("language"):
                query = query.filter(PersonaVersion.language == filters["language"])
            if filters.get("voice_type"):
                query = query.filter(PersonaVersion.voice_type == filters["voice_type"])
            if filters.get("engine"):
                query = query.filter(PersonaVersion.engine == filters["engine"])
        
    # Apply search
    if search:
        search_term = f"%{search}%"
        # We need to join with PersonaVersion to search in version fields
        query = query.outerjoin(PersonaVersion, Persona.id == PersonaVersion.persona_id)
        query = query.filter(
            or_(
                Persona.name.ilike(search_term),
                PersonaVersion.language.ilike(search_term),
                PersonaVersion.voice_type.ilike(search_term),
                PersonaVersion.engine.ilike(search_term)
            )
        ).distinct()
  
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(Persona, sort_by):
        if sort_desc:
            query = query.order_by(getattr(Persona, sort_by).desc())
        else:
            query = query.order_by(getattr(Persona, sort_by).asc())
    
    # Apply pagination and fetch results
    personas = query.offset(skip).limit(limit).all()
    
    # For each persona, fetch the active version (fallback to latest if no active version)
    for persona in personas:
        persona.current_version = get_active_persona_version(db, persona.id) or get_latest_persona_version(db, persona.id)
    
    return personas, total

def create_persona(db: Session, persona_in: PersonaCreate, created_by_id: str = None) -> Persona:
    """Create a new persona with its first version."""
    now = datetime.utcnow()
    
    # Set all other active personas for this project to archived if this one is default
    if persona_in.is_default:
        default_personas = db.query(Persona).filter(
            Persona.project_id == persona_in.project_id,
            Persona.status == PersonaStatus.ACTIVE,
            Persona.is_default == True
        ).all()
        
        for p in default_personas:
            Persona.is_default = False
            p.updated_at = now
            p.updated_by_id = created_by_id
            db.add(p)
            db.flush()
    
    # Create the new persona
    persona_obj = Persona(
        project_id=persona_in.project_id,
        name=persona_in.name,
        status=persona_in.status,
        is_default=persona_in.is_default,
        created_by_id=created_by_id,
        updated_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(persona_obj)
    db.flush()  # Flush to get the ID before creating version
    
    # Create the initial version (1.0)
    version_data = PersonaVersionCreate(
        voice_type=persona_in.voice_type,
        engine=persona_in.engine,
        language=persona_in.language,
        from_number=persona_in.from_number,
        status=PersonaStatus.ACTIVE,  # Always create the first version as active
        change_notes="Initial version"
    )
    
    version_obj = create_persona_version(
        db,
        persona_obj.id,
        version_data,
        created_by_id
    )
    
    # Add the current version to the persona object
    persona_obj.current_version = version_obj
    
    db.commit()
    db.refresh(persona_obj)
    
    return persona_obj

def update_persona(db: Session, persona_id: int, persona_in: PersonaUpdate, updated_by_id: str = None) -> Optional[Persona]:
    """Update an existing persona."""
    db_obj = db.query(Persona).filter(Persona.id == persona_id).first()
    if not db_obj:
        return None
        
    now = datetime.utcnow()
    update_data = persona_in.dict(exclude_unset=True)
    
    # Update the persona object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
        
    db_obj.updated_at = now
    db_obj.updated_by_id = updated_by_id
    
    # If setting this persona as default and it's active, unset other defaults
    if update_data.get("is_default"):
        default_personas = db.query(Persona).filter(
            Persona.project_id == db_obj.project_id,
            Persona.is_default == True,
            Persona.id != persona_id
        ).all()
        
        logger.info(f"Unsetting default personas: {default_personas}")
        
        for p in default_personas:
            p.is_default = False
            p.updated_at = now
            p.updated_by_id = updated_by_id
            db.add(p)
            db.flush()
    
    db.commit()
    db.refresh(db_obj)
    
    # Add the active version to the persona object (fallback to latest if no active version)
    db_obj.current_version = get_active_persona_version(db, db_obj.id) or get_latest_persona_version(db, db_obj.id)
    
    return db_obj

def get_persona_stats(db: Session) -> Dict[str, Any]:
    """Get persona statistics."""
    total = db.query(func.count(Persona.id)).scalar()
    
    status_counts = db.query(
        Persona.status, 
        func.count(Persona.id)
    ).group_by(Persona.status).all()
    
    project_counts = db.query(
        Persona.project_id, 
        func.count(Persona.id)
    ).group_by(Persona.project_id).all()
    
    return {
        "total": total,
        "by_status": {status: count for status, count in status_counts},
        "by_project": {str(project_id): count for project_id, count in project_counts}
    }
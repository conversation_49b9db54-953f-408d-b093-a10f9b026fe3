from sqlalchemy.orm import Session
from sqlalchemy import or_, func, and_
from app.models.questionnaire import QuestionnaireVersion, QuestionnaireStatus
from app.schemas.questionnaire import QuestionnaireVersionCreate, QuestionnaireVersionUpdate
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime

def get_questionnaire_version(db: Session, version_id: int) -> Optional[QuestionnaireVersion]:
    """Get a specific questionnaire version by ID."""
    return db.query(QuestionnaireVersion).filter(QuestionnaireVersion.id == version_id).first()

def get_questionnaire_versions(
    db: Session,
    questionnaire_id: int,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = "version",
    sort_desc: bool = True
) -> <PERSON><PERSON>[List[QuestionnaireVersion], int]:
    """Get all versions of a specific questionnaire."""
    query = db.query(QuestionnaireVersion).filter(QuestionnaireVersion.questionnaire_id == questionnaire_id)
    
    # Count total before pagination
    total = query.count()
    
    # Apply sorting
    if hasattr(QuestionnaireVersion, sort_by):
        if sort_desc:
            query = query.order_by(getattr(QuestionnaireVersion, sort_by).desc())
        else:
            query = query.order_by(getattr(QuestionnaireVersion, sort_by).asc())
    
    # Apply pagination
    versions = query.offset(skip).limit(limit).all()
    
    return versions, total

def get_latest_questionnaire_version(db: Session, questionnaire_id: int) -> Optional[QuestionnaireVersion]:
    """Get the latest version of a questionnaire."""
    return db.query(QuestionnaireVersion).filter(
        QuestionnaireVersion.questionnaire_id == questionnaire_id
    ).order_by(QuestionnaireVersion.version.desc()).first()

def get_active_questionnaire_version(db: Session, questionnaire_id: int) -> Optional[QuestionnaireVersion]:
    """Get the active version of a questionnaire."""
    return db.query(QuestionnaireVersion).filter(
        QuestionnaireVersion.questionnaire_id == questionnaire_id,
        QuestionnaireVersion.status == QuestionnaireStatus.ACTIVE
    ).first()

def create_questionnaire_version(
    db: Session, 
    questionnaire_id: int, 
    version_data: QuestionnaireVersionCreate,
    created_by_id: int = None
) -> QuestionnaireVersion:
    """
    Create a new version for a questionnaire.
    If the new version is marked as active, ensure all other versions for this questionnaire
    are set to inactive.
    """
    now = datetime.utcnow()
    
    # Find the latest version number and increment
    latest_version = get_latest_questionnaire_version(db, questionnaire_id)
    
    if latest_version:
        # Increment the version number
        major, minor = map(int, latest_version.version.split('.'))
        new_version = f"{major}.{minor + 1}"
    else:
        # First version
        new_version = "1.0"
    
    # Check if this version is set to active
    if version_data.status == QuestionnaireStatus.ACTIVE:
        # Set all other versions of this questionnaire to archived
        active_versions = db.query(QuestionnaireVersion).filter(
            QuestionnaireVersion.questionnaire_id == questionnaire_id,
            QuestionnaireVersion.status == QuestionnaireStatus.ACTIVE
        ).all()
        
        for version in active_versions:
            version.status = QuestionnaireStatus.ARCHIVED
            version.updated_at = now
    
    # Create new version
    db_obj = QuestionnaireVersion(
        questionnaire_id=questionnaire_id,
        question_text=version_data.question_text,
        question_type=version_data.question_type,
        options=version_data.options,
        order_no=version_data.order_no,
        status=version_data.status,
        version=new_version,
        change_notes=version_data.change_notes,
        created_by_id=created_by_id,
        created_at=now,
        updated_at=now
    )
    
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    
    return db_obj

def update_questionnaire_version(
    db: Session, 
    version_id: int, 
    version_data: QuestionnaireVersionUpdate
) -> Optional[QuestionnaireVersion]:
    """
    Update an existing questionnaire version.
    If status is changed to Active, ensure all other versions of this questionnaire are set to inactive.
    """
    db_obj = db.query(QuestionnaireVersion).filter(QuestionnaireVersion.id == version_id).first()
    if not db_obj:
        return None
    
    now = datetime.utcnow()
    update_data = version_data.dict(exclude_unset=True)
    
    # Check if we're activating this version
    if update_data.get("status") == QuestionnaireStatus.ACTIVE:
        # Set all other versions of this questionnaire to archived
        active_versions = db.query(QuestionnaireVersion).filter(
            QuestionnaireVersion.questionnaire_id == db_obj.questionnaire_id,
            QuestionnaireVersion.status == QuestionnaireStatus.ACTIVE,
            QuestionnaireVersion.id != version_id
        ).all()
        
        for version in active_versions:
            version.status = QuestionnaireStatus.ARCHIVED
            version.updated_at = now
    
    # Update the version object
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    db_obj.updated_at = now
    db.commit()
    db.refresh(db_obj)
    
    return db_obj
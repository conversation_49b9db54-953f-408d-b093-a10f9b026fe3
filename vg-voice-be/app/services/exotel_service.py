from typing import Any, Dict, Optional
from fastapi import WebSocket
from pipecat.pipeline.task import PipelineTask
from app.config.envconfig import settings
from app.utils.logger import get_logger
from app.schemas.exotel import ExotelInitiateCallResponse
import httpx

logger = get_logger("request_logger")


class ExotelService:
    def __init__(self):
        self.api_key = settings.exotel_api_key
        self.api_token = settings.exotel_api_token
        self.account_sid = settings.exotel_account_sid
        self.subdomain = settings.exotel_subdomain or "@api.exotel.com"
        self.base_url = f"https://{self.api_key}:{self.api_token}{self.subdomain}/v1/Accounts/{self.account_sid}"

    async def _make_request(self, endpoint: str, method: str = "POST", payload: Optional[Dict] = None) -> Dict[str, Any]:
        """Make an HTTP request to Exotel API"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Accept": "application/json"
        }

        try:
            async with httpx.AsyncClient() as client:
                if method == "POST":
                    response = await client.post(url, data=payload, headers=headers)
                elif method == "GET":
                    response = await client.get(url, headers=headers)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                    
                return await self._handle_response(response)
        except Exception as e:
            logger.error(f"Error making request to Exotel API: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to connect to Exotel API: {str(e)}"
            }

    async def _handle_response(self, response: httpx.Response) -> Dict[str, Any]:
        """Handle the HTTP response from Exotel API"""
        try:
            if response.status_code == 200:
                response_json = response.json()
                logger.info(f"Exotel API response: {response_json}")
                return response_json
            else:
                try:
                    error_resp = response.json()
                    error_message = "Unknown error"
                    
                    # Try to extract the specific error message from various possible formats
                    if "RestException" in error_resp:
                        error_message = error_resp["RestException"].get("Message", "Unknown Exotel error")
                    elif "message" in error_resp:
                        error_message = error_resp["message"]
                    
                    logger.error(f"Exotel API error: {error_resp}")
                    return {
                        "status": "error",
                        "message": error_message
                    }
                except:
                    # If we can't parse JSON from the response
                    return {
                        "status": "error",
                        "message": f"Failed to trigger call: {response.text}"
                    }
        except Exception as e:
            logger.error(f"HTTP error: {e}")
            return {
                "status": "error",
                "message": f"Failed to parse response: {str(e)}"
            }

    async def connect_to_flow(
        self,
        to_number: str,
        caller_id: str,
        flow_id: str,
        callback_url: Optional[str] = None,
        call_type: Optional[str] = "trans",
        time_limit: Optional[int] = None,
        time_out: Optional[int] = None,
        custom_field: Optional[str] = None,
        record: bool = False,
        recording_channels: str = "single",
        recording_format: str = "mp3",
        passthru_data: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Connect a call to an Exotel flow"""
        payload = {
            "From": to_number,
            "CallerId": caller_id,
            "Url": f"http://my.exotel.com/{self.account_sid}/exoml/start_voice/{flow_id}",
            "Record": "true" if record == True else "false",
            "StatusCallback": f"{settings.vg_voice_be_host}/voice/api/v1/exotel/status_callback?api_key={settings.third_party_api_key}",
            "StatusCallbackContentType": "application/json"
        }
        
        if time_limit:
            payload["TimeLimit"] = time_limit
                
        logger.info(f"Exotel connect_to_flow payload: {payload}")
        
        return await self._make_request("/Calls/connect.json", payload=payload)

    async def get_call_details(self, call_sid: str) -> Dict[str, Any]:
        """Get details of a call from Exotel"""
        endpoint = f"/Calls/{call_sid}.json"
        return await self._make_request(endpoint, method="GET")

    async def make_call_via_flow(self, to_number: str, from_number: str, flow_id: str = None, recording: bool = True, time_limit: int = 360):
        """Make a call via Exotel flow"""
        call = await self.connect_to_flow(
            to_number=to_number,
            caller_id=from_number,
            flow_id=flow_id, 
            record=recording,
            time_limit=time_limit  # default 6 minutes
        )

        if call.get("status") == "error":
            logger.error(f"Exotel call failed: {call}")
            raise Exception(call.get("message", "Unknown error occurred while making call"))
            
        call_data = call.get("Call")
        if call_data is None or call_data.get("Sid") is None:
            logger.error(f"Exotel call failed: {call}")
            raise Exception(f"Exotel call failed: {call.get('message', 'No call data returned')}")
            
        return {
            "call_sid": call_data.get("Sid"),
            "status": call_data.get("Status"),
            "from": call_data.get("From"),
            "to": call_data.get("To"),
        }

    async def format_websocket_url(self, call_id: str):
        """Format websocket URL response"""
        url = f"{settings.vg_voice_be_host_web_socket}/voice/api/v1/ws/exotel/{call_id}/{settings.third_party_api_key}"
        return ExotelInitiateCallResponse(url=url)
        
    async def get_call_recording(self, call_sid: str) -> str:
        """Get recording URL for a call"""
        endpoint = f"/Calls/{call_sid}/Recordings.json"
        recording_data = await self._make_request(endpoint, method="GET")
        if recording_data.get("Call"):
            return recording_data["Call"]["RecordingUrl"]
        return None
        
    async def end_call(self, call_sid: str, task: PipelineTask, websocket_client: WebSocket):
        """End an Exotel call by canceling the task and closing the websocket connection."""
        logger.info(f"Ending Exotel call for call_sid: {call_sid}")
        
        if task:
            await task.cancel()
            
        if websocket_client:
            try:
                await websocket_client.close(code=1000, reason="Call ended")
            except Exception as e:
                logger.error(f"Error closing websocket connection: {e}")
"""
Custom Call Post Processing Job
"""
import requests
import asyncio
from sqlalchemy.orm import Session
from datetime import datetime, timezone

from app.services.exotel_service import ExotelService
from app.services.mongo_service import MongoService
from app.utils.logger import get_logger
from app.services.s3_service import upload_file_to_s3, get_s3_static_url, upload_url_to_s3
from app.db import SessionLocal
from app.models.call import Call
from app.config.envconfig import settings
from app.models.prompt import Prompt, PromptType, PromptStatus, PromptVersion, SUMMARY_PROMPT_QUESTIONS_KEY
from app.models.call_response import CallResponse
from app.models.call import PostProcessingStatus
from app.services.llm_service import LLMService

logger = get_logger("custom_call_post_processing")

class CustomCallPostProcessor:
    def __init__(self, db: Session = None):
        if db is None:
            self.db = SessionLocal()
            self._own_db = True
        else:
            self.db = db
            self._own_db = False
        self.exotel_service = ExotelService()
        self.mongo_service = MongoService()
        self.llm_service = LLMService()
        self.mongo_collection = self.mongo_service.get_collection('call_details')

    async def process(self, call: Call, with_time_out: int = 0):
        try:
            # Enforcing timeout if it is mentioned
            if with_time_out > 0:
                logger.info(f"Enforcing timeout of {with_time_out} seconds for call - {call.id} post processing")
                await asyncio.sleep(with_time_out)
                
            if not call:
                logger.error("Call instance is None!")
                return None

            if call.post_processing_status == PostProcessingStatus.IN_PROGRESS or call.post_processing_status == PostProcessingStatus.COMPLETED:
                logger.info(f"Call {call.id} is already in progress or completed!")
                return None

            self._update_call_status(call, PostProcessingStatus.IN_PROGRESS)

            # Create mongo record if not found
            mongo_record = await self._find_or_create_mongo_record(call)

            # Audio Url
            audio_url = await self._ensure_audio_url(call)
            await self._update_mongo_record(call, {"audio_url": audio_url})

            # Call Details
            call_details = await self._get_exotel_call_details(call.request_id)
            await self._update_mongo_record(call, {"call_details": call_details})

            # Transcript
            transcript = await self._get_transcript(audio_url)
            await self._update_mongo_record(call, {"transcript": transcript})

            # Summary
            summary = await self.handle_summary_creation(call, mongo_record, transcript)
            await self._update_mongo_record(call, {"summary": summary})

            # Insights
            insights = await self.handle_insights_creation(call, mongo_record, transcript, audio_url)
            await self._update_mongo_record(call, {"insights": insights})

            # Updating Duration
            call.duration_seconds = insights.get("call_duration_in_seconds")
            self.db.add(call)
            self.db.commit()
            self.db.refresh(call)

            # Update Call Response
            self._update_call_responses(call, summary)

            self._update_call_status(call, PostProcessingStatus.COMPLETED)
        except Exception as e:
            logger.error(f"Error in custom call post processing for call {getattr(call, 'id', None)}: {str(e)}", exc_info=True)
            
            self._update_call_status(call, PostProcessingStatus.FAILED)
            return None

    async def _find_or_create_mongo_record(self, call: Call):
        query = {"request_id": call.request_id, "call_id": call.id}
        now = self._now_utc_str()
        base_doc = {
            "request_id": call.request_id,
            "call_id": call.id,
            "created_at": now,
            "updated_at": now
        }
        return await MongoService.find_or_create_record(self.mongo_collection, query, base_doc)

    async def handle_summary_creation(self, call: Call, mongo_record: dict, transcript: str):
        # cache the summary prompt details if required
        logger.info(f"Creating summary for call {call.id}")
        summary_prompt_obj = mongo_record.get("summary_prompt")

        if not summary_prompt_obj:
            persona = call.persona_version.persona
            summary_prompt = self.db.query(Prompt).filter(
                Prompt.persona_id == persona.id,
                Prompt.type == PromptType.SUMMARY,
                Prompt.status == PromptStatus.ACTIVE
            ).first()

            if not summary_prompt:
                logger.warning(f"No summary prompt found for persona_id={persona.id}")
                raise Exception(f"No summary prompt found for persona_id={persona.id}")
            
            summary_prompt_version = self.db.query(PromptVersion).filter(
                PromptVersion.prompt_id == summary_prompt.id,
                PromptVersion.status == PromptStatus.ACTIVE
            ).first()
            if not summary_prompt_version:
                logger.warning(f"No summary prompt found for persona_id={persona.id}")
                raise Exception(f"No summary prompt found for persona_id={persona.id}")

            summary_prompt_obj = {
                "prompt_version_id": summary_prompt_version.id,
                "prompt_text": summary_prompt_version.prompt_text,
            }
            await self._update_mongo_record(call, {"summary_prompt": summary_prompt_obj})
        
        # create summary
        return await self.llm_service.process_from_prompt({"transcript": transcript}, summary_prompt_obj["prompt_text"])

    async def handle_insights_creation(self, call: Call, mongo_record: dict, transcript: str, audio_url: str):
        # cache the insights prompt details if required
        insights_prompt_obj = mongo_record.get("insights_prompt")
        logger.info(f"Creating insights for call {call.id}")

        if not insights_prompt_obj:
            persona = call.persona_version.persona
            insights_prompt = self.db.query(Prompt).filter(
                Prompt.persona_id == persona.id,
                Prompt.type == PromptType.INSIGHTS,
                Prompt.status == PromptStatus.ACTIVE
            ).first()
            if not insights_prompt:
                logger.warning(f"No insights prompt found for persona_id={persona.id}")
                raise Exception(f"No insights prompt found for persona_id={persona.id}")
            
            insights_prompt_version = self.db.query(PromptVersion).filter(
                PromptVersion.prompt_id == insights_prompt.id,
                PromptVersion.status == PromptStatus.ACTIVE
            ).first()
            if not insights_prompt_version:
                logger.warning(f"No active insights prompt version found for prompt_id={insights_prompt.id}")
                raise Exception(f"No active insights prompt version found for prompt_id={insights_prompt.id}")
        
            insights_prompt_obj = {
                "prompt_version_id": insights_prompt_version.id,
                "prompt_text": insights_prompt_version.prompt_text,
            }
            await self._update_mongo_record(call, {"insights_prompt": insights_prompt_obj})
        
        # create insights
        input_data = {"transcript": transcript}
        meta_data = {"call_id": call.id, "request_id": call.request_id, "calling_agent": "CUSTOM_AI", "transcript_from": "WHISPER"}

        return await self.llm_service.post_process_intelligence(input_data, insights_prompt_obj["prompt_text"], meta_data, audio_url)

    async def _update_mongo_record(self, call: Call, update_fields: dict):
        query = {"request_id": call.request_id, "call_id": call.id}
        updated_at = self._now_utc_str()
        await MongoService.update_record(self.mongo_collection, query, update_fields, updated_at)

    def _now_utc_str(self):
        return datetime.now(timezone.utc).isoformat()

    def _update_call_status(self, call: Call, status):
        call.post_processing_status = status
        self.db.add(call)
        self.db.commit()
        self.db.refresh(call)

    async def _get_exotel_call_details(self, call_sid: str):
        return await self.exotel_service.get_call_details(call_sid)
    
    async def _get_transcript(self, audio_url: str):
        logger.info(f"Fetching transcript for audio_url: {audio_url}")
        return await self.llm_service.get_transcript(audio_url)

    def _update_call_responses(self, call: Call, summary: dict):
        """
        Updates the answer_text for each CallResponse associated with the call,
        using the summary and SUMMARY_PROMPT_QUESTIONS_KEY.
        """
        call_responses = self.db.query(CallResponse).filter_by(call_id=call.id).all()
        for call_response in call_responses:
            question_text = call_response.questionnaire_version.question_text
            if SUMMARY_PROMPT_QUESTIONS_KEY not in summary:
                logger.warning(f"SUMMARY_PROMPT_QUESTIONS_KEY missing in summary for call {call.id}")
                answer = None
            else:
                answer = summary[SUMMARY_PROMPT_QUESTIONS_KEY].get(question_text)
                logger.info(f"Extracted answer for question '{question_text}' in call {call.id}: {answer}")
            call_response.answer_text = answer
        self.db.commit()

    async def _ensure_audio_url(self, call: Call):
        """
        Ensures the call has an audio_url. If not, fetches from Exotel and uploads to S3.
        Updates the call in DB if needed.
        """
        if call.audio_url:
            logger.info(f"Call {call.id} already has audio_url: {call.audio_url}")
            return call.audio_url
        recording_url = await self.exotel_service.get_call_recording(call.request_id)
        if not recording_url:
            logger.error(f"No recording URL from Exotel for call {call.id}")
            return None
        s3_key = f"{call.request_id}.mp3"
        s3_url = upload_url_to_s3(s3_key, recording_url, (settings.exotel_api_key, settings.exotel_api_token))

        call.audio_url = s3_url
        self.db.add(call)
        self.db.commit()
        self.db.refresh(call)
        logger.info(f"Updated call {call.id} with audio_url: {s3_url}")
        return s3_url


    def __del__(self):
        if getattr(self, '_own_db', False):
            self.db.close()

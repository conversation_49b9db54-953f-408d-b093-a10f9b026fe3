"""FastAPI application for Vegrow Voice Backend."""

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html

from app.api.v1.api import api_router
from app.middlewares.auth import AuthMiddleware
from app.middlewares.logger import LoggerMiddleware

description = """
# Vegrow Voice API

Vegrow Voice is an AI Calling Platform that allows internal users to trigger structured AI-powered voice calls via projects.

## Key Features

* **Projects** - Create and manage calling projects with specific configurations
* **Personas** - Create voice personas with versioning for different use cases
* **Calls** - Trigger individual calls or batch calls with CSV uploads

## Authentication

All API calls require an Authorization header with a valid token.
"""

tags_metadata = [
    {
        "name": "projects",
        "description": "Operations with projects. Projects are the top-level containers for personas, prompts, and calls.",
    },
    {
        "name": "personas",
        "description": "Operations with personas. Personas define the voice identity for calls, including name, voice type, and language.",
    },
    {
        "name": "persona versions",
        "description": "Operations with persona versions. Each persona can have multiple versions with only one active at a time.",
    },
    {
        "name": "calls",
        "description": "Operations for triggering and managing voice calls.",
    },
    {
        "name": "statistics",
        "description": "Get statistics and analytics about projects, personas, and calls.",
    },
]

app = FastAPI(
    title="Vegrow Voice API",
    description=description,
    version="1.0.0",
    openapi_tags=tags_metadata,
    docs_url=None,  # Disable default docs URL
    redoc_url=None,  # Disable default redoc URL
    openapi_url="/voice/api/openapi.json",  # Customize OpenAPI URL
    contact={
        "name": "Vegrow Tech Team",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "Private",
        "url": "https://vegrow.in/terms-of-service",
    },
)

# CORS setup
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

app.add_middleware(LoggerMiddleware)
app.add_middleware(AuthMiddleware)
app.include_router(api_router, prefix="/voice/api/v1")

# Custom Swagger UI route
@app.get("/voice/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Custom Swagger UI HTML
    """
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css",
    )

# Custom ReDoc route
@app.get("/voice/redoc", include_in_schema=False)
async def redoc_html():
    """
    Custom ReDoc HTML
    """
    return get_redoc_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - ReDoc",
        redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
    )

@app.get("/ping")
async def ping():
    """
    A simple ping endpoint that returns a success response
    """
    return JSONResponse(
        content={"message": "success"},
        status_code=200
    )

def custom_openapi():
    """
    Customize OpenAPI schema with additional information
    """
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
        tags=app.openapi_tags,
    )
    
    # Add security scheme
    openapi_schema["components"] = {
        "securitySchemes": {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }
    }
    
    # Apply security globally
    openapi_schema["security"] = [{"bearerAuth": []}]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
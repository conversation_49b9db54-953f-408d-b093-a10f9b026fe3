import asyncio
from datetime import datetime
from typing import Any
from fastapi import WebSocket
from pipecat.audio.vad.silero import SileroVA<PERSON>nal<PERSON><PERSON>
from pipecat.audio.vad.vad_analyzer import VADParams
from pipecat.frames.frames import LLMMessagesAppendFrame, EndTaskFrame
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import <PERSON><PERSON>ine<PERSON><PERSON><PERSON>
from pipecat.pipeline.task import PipelineParams, PipelineTask
from pipecat.processors.frame_processor import FrameDirection
from pipecat.serializers.base_serializer import FrameSerializer
from pipecat.services.gemini_multimodal_live.gemini import GeminiMultimodalLiveLLMService, InputParams
from pipecat.services.google.llm import GoogleLLMContext
from pipecat.transports.network.fastapi_websocket import (
    FastAPIWebsocketParams,
    FastAPIWebsocketTransport,
)
from pipecat.transcriptions.language import Language

from app.utils.logger import get_logger
from app.config.envconfig import settings
from app.services.exotel_service import ExotelService
from app.custom_call.rtvi.persistent_context import Persistent<PERSON>ontext
from app.custom_call.vad.silence_detector import SilenceDete<PERSON><PERSON>ara<PERSON>, SilenceDetectorProcessor
from app.models.persona import CustomAIVoiceType, PersonaLanguage
from app.services.prompt_service import replace_variables_in_prompt_text
from app.db import get_db_session


logger = get_logger("request_logger")

async def websocket_audio_2_audio_pipeline(
    call: any,
    prompt_version: any,
    persona_version: any,
    websocket_client: WebSocket,
    serializer: FrameSerializer,
) -> Pipeline:
    logger.info(f"Creating WebSocket pipeline for conversation {call.id}")

    ended_by = "Client"

    # Setup VAD analyzer
    vad_analyzer = SileroVADAnalyzer(params=VADParams(
        confidence=0.7,
        start_secs=0.25,
        stop_secs=0.5,
        min_volume=0.35
    ))

    # Setup transport
    transport = FastAPIWebsocketTransport(
        websocket=websocket_client,
        params=FastAPIWebsocketParams(
            audio_in_enabled=True,
            audio_out_enabled=True,
            add_wav_header=False,
            vad_enabled=True,
            vad_analyzer=vad_analyzer,
            vad_audio_passthrough=True,
            serializer=serializer,
        ),
    )

    messages = []
    prompt_text = replace_variables_in_prompt_text(prompt_version.prompt_text, call.variables)
    messages.append({
        "role": "system",
        "content": prompt_text
    })
    messages.append({
        "role": "user",
        "content": "Hello"
    })
    logger.info(f"Retrieved {len(messages)} messages from conversation")

    # Define callable terminate_call function
    async def terminate_call(function_name, tool_call_id, args, llm, context, result_callback):
        nonlocal ended_by
        ended_by = "AI Bot"
        await llm.queue_frame(EndTaskFrame(), FrameDirection.UPSTREAM)

    # Setup tools and register functions
    available_functions = []
    available_functions.append({"name": "terminate_call", "description": "Terminate the call"})
    tools = [{"function_declarations": available_functions}]

    llm_rt = GeminiMultimodalLiveLLMService(
        api_key=str(settings.gemini_api_key),
        voice_id=persona_version.voice_type or CustomAIVoiceType.LEDA,
        transcribe_user_audio=False,
        transcribe_model_audio=True,
        inference_on_context_initialization=True,
        tools=tools,
        # params=InputParams(
        #     language=PersonaLanguage.get_language_code(persona_version.language) or Language.HI_IN,
        # )
    )

    llm_rt.register_function("terminate_call", terminate_call)

    # Create context and aggregators
    context_rt = GoogleLLMContext(messages, tools)
    context_aggregator_rt = llm_rt.create_context_aggregator(context_rt)
    user_aggregator = context_aggregator_rt.user()
    assistant_aggregator = context_aggregator_rt.assistant()
    await llm_rt.set_context(context_rt)
    
    # Create storage
    storage = PersistentContext(context=context_rt)

    # Define the message handler using the decorator
    @storage.on_context_message
    async def on_context_message(messages: list[Any]):
        logger.debug(f"{len(messages)} message(s) received for storage")
    
    # Silence detector
    silence_detector = SilenceDetectorProcessor(
        params=SilenceDetectorParams(
            silence_threshold_secs=10.0,
            prompt_cooldown_secs=15.0,
            initial_wait_secs=10.0,
            max_consecutive_silences=2,
        )
    )

    # Processors
    processors = [
        transport.input(),
        silence_detector,
        user_aggregator,
        llm_rt,
        transport.output(),
        assistant_aggregator,
        storage.create_processor(exit_on_endframe=True),
    ]

    # create pipeline & task
    pipeline = Pipeline(processors)
    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            audio_in_sample_rate=8000,
            audio_out_sample_rate=8000,
            allow_interruptions=True,
            enable_metrics=True,
            send_initial_empty_metrics=False,
        ),
    )

    # Event handlers
    @transport.event_handler("on_client_connected")
    async def on_client_connected(transport, client):
        logger.info("Client connected")
        await task.queue_frames([user_aggregator.get_context_frame()])

    @transport.event_handler("on_client_disconnected")
    async def on_client_disconnected(transport, client):
        logger.info("Client disconnected")
        try:
            async with get_db_session() as db:
                db_call = db.query(type(call)).filter(type(call).id == call.id).first()
                if db_call:
                    db_call.status = "completed"
                    db_call.ended_by = ended_by
                    db.commit()
                    
        except Exception as e:
            logger.error(f"Error updating conversation status: {e}")
        try:
            await asyncio.wait_for(task.cancel(), timeout=5.0)
            logger.info("Pipeline task cancelled successfully")
        except asyncio.TimeoutError:
            logger.error("Pipeline cancellation timed out")
        except Exception as e:
            logger.error(f"Error during pipeline cancellation: {e}")

    @silence_detector.event_handler("on_extended_silence")
    async def handle_extended_silence(detector, duration):
        logger.info(f"Extended silence of {duration:.2f}s detected - prompting user")
        if detector.consecutive_silences < detector.params.max_consecutive_silences:
            await task.queue_frames([
                LLMMessagesAppendFrame(messages=[{
                    "role": "user",
                    "content": "This is an induced message by silence detector. Consider this as system message. Message: User is silent for a long time check if he is available or not"
                }])
            ])

    @silence_detector.event_handler("on_max_silence_exceeded")
    async def handle_max_silence_exceeded(detector, count):
        logger.warning(f"Maximum consecutive silences ({count}) detected - ending call")
        async with get_db_session() as db:
            db_call = db.query(type(call)).filter(type(call).id == call.id).first()
            if db_call:
                db_call.status = "completed"
                db.commit()

        nonlocal ended_by
        ended_by = "AI Bot"
        await ExotelService.end_call(call.request_id, task, websocket_client)

    # Run the pipeline
    runner = PipelineRunner(handle_sigint=False, force_gc=True)
    await runner.run(task)

    return pipeline
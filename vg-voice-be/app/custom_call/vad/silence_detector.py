import time
from typing import Optional
from pydantic import BaseModel
from app.utils.logger import get_logger
from pipecat.frames.frames import Frame, BotStartedSpeakingFrame, UserStartedSpeakingFrame, UserStoppedSpeakingFrame, BotStoppedSpeakingFrame, BotSpeakingFrame
from pipecat.processors.frame_processor import FrameProcessor, FrameDirection

logger = get_logger("silence_detector")

class SilenceDetectorParams(BaseModel):
    """Parameters for silence detection."""
    silence_threshold_secs: float = 2.0
    prompt_cooldown_secs: float = 10.0
    initial_wait_secs: float = 5.0  # Default wait time before starting silence detection
    max_consecutive_silences: int = 2  # Maximum number of consecutive silences before ending call


class SilenceDetectorProcessor(FrameProcessor):
    """Processor that monitors for extended silence periods."""
    
    def __init__(self, params: Optional[SilenceDetectorParams] = None, **kwargs):
        super().__init__(**kwargs)
        self.params = params or SilenceDetectorParams()
        
        # State tracking
        self.is_silent = True
        self.last_speech_time = time.time()
        self.last_prompt_time = None
        self.start_time = time.time()  # Track when the processor was created
        self.detection_active = False  # Flag to indicate if silence detection is active
        self.consecutive_silences = 0  # Count consecutive silences
        
        # Register event handlers
        self._register_event_handler("on_extended_silence")
        self._register_event_handler("on_max_silence_exceeded")
    
    async def process_frame(self, frame: Frame, direction: FrameDirection):
        """Process frames to detect extended silence."""
        await super().process_frame(frame, direction)
        
        current_time = time.time()
        
        # Check if we've passed the initial wait period
        if not self.detection_active:
            if current_time - self.start_time >= self.params.initial_wait_secs:
                logger.info(f"Silence detection activated after {self.params.initial_wait_secs} seconds wait")
                self.detection_active = True
                # Reset the last speech time to now to avoid immediate silence trigger
                self.last_speech_time = current_time
        
        # Track speech state from user speech events
        if isinstance(frame, UserStartedSpeakingFrame):
            self.is_silent = False
            self.last_speech_time = current_time
            # Reset consecutive silences when speech is detected
            self.consecutive_silences = 0
        elif isinstance(frame, BotStartedSpeakingFrame) or isinstance(frame, BotSpeakingFrame):
            self.is_silent = False
            self.last_speech_time = current_time
        elif isinstance(frame, UserStoppedSpeakingFrame) or isinstance(frame, BotStoppedSpeakingFrame):
            self.is_silent = True
        
        # Check for extended silence only if detection is active
        if self.detection_active and self.is_silent:
            silence_duration = current_time - self.last_speech_time
            
            if silence_duration >= self.params.silence_threshold_secs:
                # Check if we're in cooldown period
                if (self.last_prompt_time is None or 
                    current_time - self.last_prompt_time >= self.params.prompt_cooldown_secs):
                    
                    # Increment consecutive silences counter
                    self.consecutive_silences += 1
                    logger.info(f"Consecutive silences detected: {self.consecutive_silences}")
                    
                    await self._call_event_handler("on_extended_silence", silence_duration)
                    self.last_prompt_time = current_time
                    
                    # Check if we've exceeded the maximum consecutive silences
                    if self.consecutive_silences >= self.params.max_consecutive_silences:
                        logger.warning(f"Maximum consecutive silences ({self.consecutive_silences}) exceeded.")
                        await self._call_event_handler("on_max_silence_exceeded", self.consecutive_silences)
        
        await self.push_frame(frame, direction)
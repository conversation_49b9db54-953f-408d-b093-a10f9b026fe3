import base64
import json
from typing import Optional, Dict, Any

import numpy as np
from pydantic import BaseModel
from app.utils.logger import get_logger
from pipecat.audio.utils import create_default_resampler
from pipecat.frames.frames import (
    AudioRawFrame,
    Frame,
    InputAudioRawFrame,
    InputDTMFFrame,
    KeypadEntry,
    StartFrame,
    StartInterruptionFrame,
    TransportMessageFrame,
    TransportMessageUrgentFrame
)
from pipecat.serializers.base_serializer import FrameSerializer, FrameSerializerType

logger = get_logger("exotel_frame_serializer")

class ExotelFrameSerializer(FrameSerializer):
    class InputParams(BaseModel):
        exotel_sample_rate: int = 8000  # Default Exotel rate (8kHz)
        sample_rate: Optional[int] = None  # Pipeline input rate

    def __init__(self, stream_sid: str, params: InputParams = InputParams()):
        self._stream_sid = stream_sid
        self._params = params
        self._exotel_sample_rate = self._params.exotel_sample_rate
        self._sample_rate = 0  # Pipeline input rate
        self._resampler = create_default_resampler()
        logger.info(f"Initialized ExotelFrameSerializer with stream_sid: {stream_sid}")

    @property
    def type(self) -> FrameSerializerType:
        return FrameSerializerType.TEXT

    async def setup(self, frame: StartFrame):
        self._sample_rate = self._params.sample_rate or frame.audio_in_sample_rate
        logger.info(f"Setup completed with sample rate: {self._sample_rate}")

    async def serialize(self, frame: Frame) -> str | bytes | None:
        if isinstance(frame, StartInterruptionFrame):
            answer = {"event": "clear", "stream_sid": self._stream_sid}
            return json.dumps(answer)
        elif isinstance(frame, AudioRawFrame):
            data = frame.audio

            # Exotel: Media in the payloads are sent in raw/slin (16-bit, 8kHz, mono PCM (little-endian)) encoded in base64.
            out_pcm_bytes = await self._resampler.resample(data,  self._sample_rate, self._exotel_sample_rate)
            payload = base64.b64encode(out_pcm_bytes).decode("utf-8")
            answer = {
                "event": "media",
                "stream_sid": self._stream_sid,
                "media": {"payload": payload},
            }
            return json.dumps(answer)
        elif isinstance(frame, (TransportMessageFrame, TransportMessageUrgentFrame)):
            return json.dumps(frame.message)
        else:
            logger.warning(f"Unsupported frame type: {type(frame)}")
            return None

    async def deserialize(self, data: str | bytes) -> Frame | None:
        message = json.loads(data)
        if message["event"] == "media":
            payload_base64 = message["media"]["payload"]
            payload = base64.b64decode(payload_base64)

            # Exotel: Media in the payloads are sent in raw/slin (16-bit, 8kHz, mono PCM (little-endian)) encoded in base64.
            out_pcm_bytes = await self._resampler.resample(payload,  self._exotel_sample_rate, self._sample_rate)
            audio_frame = InputAudioRawFrame(
                audio=out_pcm_bytes, num_channels=1, sample_rate=self._sample_rate
            )
            return audio_frame
            
        elif message["event"] == "dtmf":
            digit = message.get("dtmf", {}).get("digit")
            
            try:
                return InputDTMFFrame(KeypadEntry(digit))
            except ValueError as e:
                return None
        else:
            logger.warning(f"Unsupported event type: {message['event']}")
            return None

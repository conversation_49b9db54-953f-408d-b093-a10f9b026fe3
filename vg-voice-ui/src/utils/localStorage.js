/**
 * Local storage utility functions for managing user data
 */

// In-memory cache for better performance
let userObj = null;

/**
 * Save user data to local storage and memory
 * @param {Object} user - User data to save
 */
export const saveUserData = (user) => {
  try {
    if (!user) {
      console.error('Attempted to save null or undefined user data');
      return;
    }

    userObj = user;
    localStorage.setItem('user', JSON.stringify(userObj));

    // Verify data was saved correctly
  } catch (error) {
    console.error('Error saving user data to localStorage:', error);
  }
};

/**
 * Remove user data from local storage and memory
 */
export const removeUser = () => {
  userObj = null;
  localStorage.removeItem('user');
};

/**
 * Get user data from memory or local storage
 * @returns {Object|null} User data or null if not logged in
 */
export const getUserData = () => {
  if (userObj) {
    return userObj;
  }

  try {
    // Use localStorage.getItem to avoid issues with direct property access
    const userStr = localStorage.getItem('user');

    // Only attempt to parse if we have an actual string
    if (userStr && typeof userStr === 'string' && userStr.trim() !== '') {
      userObj = JSON.parse(userStr);
    } else {
      userObj = null;
    }
  } catch (error) {
    console.error('Error parsing user data from localStorage:', error);
    // If parsing fails, reset the stored value to prevent future errors
    localStorage.removeItem('user');
    userObj = null;
  }

  return userObj;
};

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isAuthenticated = () => {
  try {
    // First, directly check for the authToken (our new direct token storage)
    const directToken = localStorage.getItem('authToken');
    if (directToken) {
      return true;
    }

    // Fallback to checking user data
    const user = getUserData();
    if (!user) {
      return false;
    }

    // Check for token in user object
    const hasToken = !!user.authentication_token;
    return hasToken;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
};

/**
 * Get geolocation coordinates for API requests
 * Note: This returns a Promise that resolves to coordinates
 * @returns {Promise<{latitude: string, longitude: string}>} Promise with coordinates
 */
export const getCoordinates = () => {
  return new Promise((resolve) => {
    // Default to Bangalore coordinates
    const defaultCoords = {
      latitude: '12.9716',
      longitude: '77.5946',
    };

    // Try to get actual location if browser supports it
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude.toString(),
            longitude: position.coords.longitude.toString(),
          });
        },
        () => {
          // On error, use default coordinates
          resolve(defaultCoords);
        }
      );
    } else {
      // Geolocation not supported
      resolve(defaultCoords);
    }
  });
};

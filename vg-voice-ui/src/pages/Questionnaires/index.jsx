import React, { useEffect, useState, useCallback } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import {
  listQuestionnaires,
  createQuestionnaire,
  updateQuestionnaire,
  deleteQuestionnaire,
  getQuestionnaireVersions,
  createQuestionnaireVersion,
  updateQuestionnaireVersion,
} from '../../services/questionnaireService';
import QuestionnaireFormModal from './QuestionnaireFormModal';
import { Link } from '@mui/material';
import VersionsDialog from '../../components/VersionsDialog/VersionsDialog';
import { statusOptions, questionTypeOptions } from '../../utils/Constants/Questionnaries';

const getColumnsBase = (handleOpenVersionsDialog) => [
  {
    headerName: 'Question Text',
    field: 'current_version.question_text',
    valueGetter: (params) => params.row.current_version?.question_text || '',
    renderCell: ({ row, value }) => (
      <Link
        component='button'
        variant='body2'
        onClick={(e) => {
          e.stopPropagation();
          handleOpenVersionsDialog(row);
          return false;
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    headerName: 'Question Type',
    field: 'current_version.question_type',
    valueGetter: (params) => params.row.current_version?.question_type || '',
  },
  { headerName: 'Project ID', field: 'project_id' },
  { headerName: 'Status', field: 'status' },
  {
    headerName: 'Order No',
    field: 'current_version.order_no',
    valueGetter: (params) => params.row.current_version?.order_no || '',
  },
  {
    headerName: 'Version',
    field: 'current_version.version',
    valueGetter: (params) => params.row.current_version?.version || '',
  },
];

const Questionnaires = ({ projectId, inProjectDetail = false, setError: setParentError }) => {
  const [questionnaires, setQuestionnaires] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    project_id: projectId || 1,
    question_text: '',
    question_type: '',
    options: null,
    status: '',
    order_no: null,
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  const [versionsDialogOpen, setVersionsDialogOpen] = useState(false);
  const [selectedQuestionnaire, setSelectedQuestionnaire] = useState(null);
  const [questionnaireVersions, setQuestionnaireVersions] = useState([]);

  const fetchQuestionnaires = useCallback(
    async (page = 0, limit = 10) => {
      setLoading(true);
      setError(null);
      try {
        const params = { skip: page * limit, limit };
        if (projectId) {
          params.project_id = projectId;
        }
        const res = await listQuestionnaires(params);
        const items = res.items || [];
        setQuestionnaires(items);
        setTotalCount(res.metadata?.total ?? items.length);
      } catch (err) {
        const errorMsg = 'Failed to load questionnaires';
        setError(errorMsg);
        if (setParentError) setParentError(errorMsg);
      } finally {
        setLoading(false);
      }
    },
    [projectId, setParentError]
  );

  useEffect(() => {
    fetchQuestionnaires(page, rowsPerPage);
  }, [fetchQuestionnaires, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalMode('create');
    setModalInitialValues({
      question_text: '',
      question_type: 'Text',
      order_no: 1,
      status: 'Draft',
    });
    setModalOpen(true);
    setActionError(null);
  };

  // EDIT
  const handleOpenEdit = (questionnaire) => {
    setModalMode('edit');
    const formValues = {
      id: questionnaire.id,
      status: questionnaire.status || 'Draft',
    };
    setModalInitialValues(formValues);
    setModalOpen(true);
    setActionError(null);
  };

  // DELETE
  const handleDelete = async (questionnaireId) => {
    if (!window.confirm('Are you sure you want to delete this questionnaire?')) return;
    setActionLoading(true);
    setActionError(null);
    try {
      await deleteQuestionnaire(questionnaireId);
      fetchQuestionnaires(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to delete questionnaire');
    } finally {
      setActionLoading(false);
    }
  };

  // ROW CLICK - Navigate to questionnaire detail (if needed)
  const handleRowClick = (row) => {
    // Uncomment if you want to navigate to a detail page
    // if (row && row.id) {
    //   navigate(`/questionnaires/${row.id}`);
    // }
  };

  // SUBMIT (CREATE/EDIT)
  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'edit') {
        const changedFields = {};

        changedFields.id = values.id;

        Object.keys(values).forEach((key) => {
          if (JSON.stringify(values[key]) !== JSON.stringify(modalInitialValues[key])) {
            changedFields[key] = values[key];
          }
        });

        await updateQuestionnaire(values.id, changedFields);
      } else {
        // For create, ensure all required fields are present
        const createData = {
          question_text: values.question_text,
          question_type: values.question_type,
          order_no: values.order_no,
          status: values.status,
          project_id: values.project_id,
        };
        console.log('Creating questionnaire with data:', createData);
        await createQuestionnaire(createData);
      }
      setModalOpen(false);
      fetchQuestionnaires(page, rowsPerPage);
    } catch (err) {
      console.error('Error saving questionnaire:', err);
      setActionError('Failed to save questionnaire');
    } finally {
      setActionLoading(false);
    }
  };

  const handleOpenVersionsDialog = (questionnaire) => {
    setSelectedQuestionnaire(questionnaire);
    setVersionsDialogOpen(true);
    fetchQuestionnaireVersions(questionnaire.id);
  };

  const fetchQuestionnaireVersions = async (questionnaireId) => {
    setActionLoading(true);
    try {
      const response = await getQuestionnaireVersions(questionnaireId);
      setQuestionnaireVersions(response || []);
      return response;
    } catch (err) {
      console.error('Error loading questionnaire versions:', err);
      setActionError('Failed to load questionnaire versions');
      return [];
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreateQuestionnaireVersion = async (questionnaireId, versionData) => {
    setActionLoading(true);
    try {
      await createQuestionnaireVersion(questionnaireId, versionData);
      if (selectedQuestionnaire) {
        await fetchQuestionnaireVersions(selectedQuestionnaire.id);
        // Refresh questionnaires list to get the updated current_version
        fetchQuestionnaires(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error creating questionnaire version:', err);
      setActionError('Failed to create questionnaire version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdateQuestionnaireVersion = async (versionId, versionData) => {
    setActionLoading(true);
    try {
      await updateQuestionnaireVersion(versionId, versionData);
      if (selectedQuestionnaire) {
        await fetchQuestionnaireVersions(selectedQuestionnaire.id);
        // Refresh questionnaires list to get the updated current_version
        fetchQuestionnaires(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error updating questionnaire version:', err);
      setActionError('Failed to update questionnaire version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  // Get columns with actions
  const getColumns = () => {
    const columnsBase = getColumnsBase(handleOpenVersionsDialog);
    return [
      ...columnsBase,
      {
        headerName: 'Actions',
        field: 'actions',
        renderCell: ({ row }) => (
          <div style={{ display: 'flex', gap: 8 }}>
            <Button size='small' variant='outlined' onClick={() => handleOpenEdit(row)}>
              Edit
            </Button>
            <Button size='small' variant='outlined' onClick={() => handleOpenVersionsDialog(row)}>
              Versions
            </Button>
          </div>
        ),
      },
    ];
  };

  const columns = getColumns();

  return (
    <div>
      {inProjectDetail && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 24,
          }}
        >
          <h2>Questionnaires</h2>
          <Button onClick={handleOpenCreate}>+ Create Questionnaire</Button>
        </div>
      )}
      {error && <div style={{ color: 'red' }}>{error}</div>}
      {actionError && <div style={{ color: 'red' }}>{actionError}</div>}
      <DataTable
        data={questionnaires}
        columns={columns}
        loading={loading || actionLoading}
        totalCount={
          questionnaires.length > 0 ? Math.max(totalCount, questionnaires.length) : totalCount
        }
        serverSidePagination
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        emptyStateMessage={loading ? 'Loading...' : 'No questionnaires found'}
        onRowClick={handleRowClick}
        getRowId={(row) => row.id}
        searchEnabled={false}
      />
      <QuestionnaireFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
        projectId={projectId}
      />

      {/* Versions Dialog */}
      {selectedQuestionnaire && (
        <VersionsDialog
          open={versionsDialogOpen}
          onClose={() => setVersionsDialogOpen(false)}
          versions={questionnaireVersions}
          entity={selectedQuestionnaire}
          fetchVersions={fetchQuestionnaireVersions}
          createVersion={handleCreateQuestionnaireVersion}
          updateVersion={handleUpdateQuestionnaireVersion}
          fields={[
            { name: 'question_text', label: 'Question Text', type: 'textarea', required: true },
            {
              name: 'question_type',
              label: 'Question Type',
              type: 'select',
              required: true,
              options: questionTypeOptions,
            },
            { name: 'options', label: 'Options', type: 'textarea', required: false },
            { name: 'order_no', label: 'Order Number', type: 'number', required: true },
            {
              name: 'status',
              label: 'Status',
              type: 'select',
              required: true,
              options: statusOptions,
            },
            { name: 'change_notes', label: 'Change Notes', type: 'textarea', required: true },
          ]}
          entityType='questionnaire'
        />
      )}
    </div>
  );
};

export default Questionnaires;

import React, { useState, useEffect } from 'react';
import ResponsiveTabs from '../../components/Tabs/ResponsiveTabs';
import { Card, Box, Typography, Alert, CircularProgress } from '@mui/material';
import Personas from '../Personas';
import Prompts from '../Prompts';
import Questionnaires from '../Questionnaires';
import { useParams } from 'react-router-dom';
import { getProjectById } from '../../services/projectsService';
import { useProject } from '../../contexts/ProjectContext';

const tabList = [
  { label: 'Personas', value: 'personas' },
  { label: 'Prompts', value: 'prompts' },
  { label: 'Questionnaire', value: 'questionnaire' },
];

const ProjectDetail = () => {
  const [tab, setTab] = useState('personas');
  const [error, setError] = useState(null);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id: urlProjectId } = useParams();
  const { selectedProjectId } = useProject();
  const projectId = urlProjectId || selectedProjectId;

  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId) return;

      setLoading(true);
      setError(null);
      try {
        const response = await getProjectById(projectId);
        setProject(response);
      } catch (err) {
        console.error('Error loading project:', err);
        setError('Failed to load project details');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [projectId]);

  if (!projectId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='info'>
          Please select a project from the dropdown in the header to view its details.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1100, margin: '0 auto', p: 3 }}>
      <ResponsiveTabs
        tabs={tabList}
        value={tabList.findIndex((t) => t.value === tab)}
        onChange={(_, value) => {
          setTab(tabList[value].value);
        }}
      />
      <Box sx={{ mt: 3 }}>
        {tab === 'personas' && (
          <Card sx={{ p: 3 }}>
            <Personas projectId={projectId} inProjectDetail={true} setError={setError} />
          </Card>
        )}
        {tab === 'prompts' && (
          <Card sx={{ p: 3 }}>
            <Prompts projectId={projectId} inProjectDetail={true} setError={setError} />
          </Card>
        )}
        {tab === 'questionnaire' && (
          <Card sx={{ p: 3 }}>
            <Questionnaires projectId={projectId} inProjectDetail={true} setError={setError} />
          </Card>
        )}
      </Box>
    </Box>
  );
};

export default ProjectDetail;

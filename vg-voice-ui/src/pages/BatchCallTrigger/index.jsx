import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Stepper,
  Step,
  StepLabel,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Link,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  ArrowBack as ArrowBackIcon,
  PlayArrow as PlayArrowIcon,
  Download as DownloadIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const BatchCallTrigger = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState([]);
  const [personas, setPersonas] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [file, setFile] = useState(null);
  const [previewData, setPreviewData] = useState([]);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [batchId, setBatchId] = useState(null);
  const [formData, setFormData] = useState({
    project_id: '',
    persona_id: '',
    prompt_id: '',
    batch_name: '',
    description: '',
  });

  const navigate = useNavigate();

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    if (formData.project_id) {
      fetchPersonas(formData.project_id);
      fetchPrompts(formData.project_id);
    }
  }, [formData.project_id]);

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'http://localhost:7004'}/voice/api/v1/projects`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      showAlert('Error fetching projects', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchPersonas = async (projectId) => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'http://localhost:7004'}/voice/api/v1/personas?project_id=${projectId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setPersonas(data.personas || []);
      }
    } catch (error) {
      console.error('Error fetching personas:', error);
      showAlert('Error fetching personas', 'error');
    }
  };

  const fetchPrompts = async (projectId) => {
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'http://localhost:7004'}/voice/api/v1/prompts?project_id=${projectId}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setPrompts(data.prompts || []);
      }
    } catch (error) {
      console.error('Error fetching prompts:', error);
      showAlert('Error fetching prompts', 'error');
    }
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setFile(selectedFile);

      // Preview the CSV file
      const reader = new FileReader();
      reader.onload = (e) => {
        const csvText = e.target.result;
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');

        const previewRows = [];
        for (let i = 1; i < Math.min(lines.length, 6); i++) {
          if (lines[i].trim() === '') continue;

          const values = lines[i].split(',');
          const row = {};
          headers.forEach((header, index) => {
            row[header.trim()] = values[index]?.trim() || '';
          });
          previewRows.push(row);
        }

        setPreviewData(previewRows);
      };
      reader.readAsText(selectedFile);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleNext = () => {
    if (activeStep === 0 && !validateStep1()) {
      showAlert('Please fill out all required fields', 'error');
      return;
    }

    if (activeStep === 1 && !file) {
      showAlert('Please upload a CSV file', 'error');
      return;
    }

    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const validateStep1 = () => {
    return formData.project_id && formData.persona_id && formData.prompt_id && formData.batch_name;
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const formDataObj = new FormData();
      formDataObj.append('file', file);
      formDataObj.append('project_id', formData.project_id);
      formDataObj.append('persona_id', formData.persona_id);
      formDataObj.append('prompt_id', formData.prompt_id);
      formDataObj.append('batch_name', formData.batch_name);
      formDataObj.append('description', formData.description || '');

      const response = await fetch(
        `${process.env.REACT_APP_API_URL || 'http://localhost:7004'}/voice/api/v1/batches`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth_token')}`,
          },
          body: formDataObj,
        }
      );

      if (response.ok) {
        const data = await response.json();
        setBatchId(data.batch_id);
        setShowSuccessDialog(true);
      } else {
        const errorData = await response.json();
        showAlert(`Error: ${errorData.message || 'Failed to create batch'}`, 'error');
      }
    } catch (error) {
      console.error('Error submitting batch:', error);
      showAlert('Error submitting batch', 'error');
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = () => {
    // Create a simple CSV template
    const csvContent =
      'phone_number,name,location\n+919876543210,John Doe,Mumbai\n+918765432109,Jane Smith,Delhi';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'call_batch_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const showAlert = (message, severity) => {
    setAlert({ show: true, message, severity });
    setTimeout(() => {
      setAlert({ show: false, message: '', severity: 'success' });
    }, 5000);
  };

  const handleViewCallLogs = () => {
    navigate('/call-logs');
  };

  const handleClose = () => {
    setShowSuccessDialog(false);
    resetForm();
    navigate('/call-logs');
  };

  const resetForm = () => {
    setActiveStep(0);
    setFormData({
      project_id: '',
      persona_id: '',
      prompt_id: '',
      batch_name: '',
      description: '',
    });
    setFile(null);
    setPreviewData([]);
  };

  const steps = ['Configure Batch', 'Upload Data', 'Confirm and Trigger'];

  return (
    <Container maxWidth='md'>
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
        <IconButton onClick={() => navigate(-1)} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant='h4'>Batch Call Trigger</Typography>
      </Box>

      {alert.show && (
        <Alert
          severity={alert.severity}
          sx={{ mb: 2 }}
          onClose={() => setAlert({ ...alert, show: false })}
        >
          {alert.message}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 4 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {activeStep === 0 && (
          <Box>
            <Typography variant='h6' gutterBottom>
              Configure Batch Settings
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  name='batch_name'
                  label='Batch Name'
                  value={formData.batch_name}
                  onChange={handleInputChange}
                  fullWidth
                  required
                  margin='normal'
                  placeholder='e.g. Farmer Profiling May 2025'
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name='description'
                  label='Description (Optional)'
                  value={formData.description}
                  onChange={handleInputChange}
                  fullWidth
                  multiline
                  rows={2}
                  margin='normal'
                  placeholder='Brief description of this batch'
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth margin='normal'>
                  <InputLabel>Project *</InputLabel>
                  <Select
                    name='project_id'
                    value={formData.project_id}
                    onChange={handleInputChange}
                    label='Project *'
                    required
                  >
                    {projects.map((project) => (
                      <MenuItem key={project.id} value={project.id}>
                        {project.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth margin='normal'>
                  <InputLabel>Persona *</InputLabel>
                  <Select
                    name='persona_id'
                    value={formData.persona_id}
                    onChange={handleInputChange}
                    label='Persona *'
                    required
                    disabled={!formData.project_id}
                  >
                    {personas.map((persona) => (
                      <MenuItem key={persona.id} value={persona.id}>
                        {persona.name} ({persona.language})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth margin='normal'>
                  <InputLabel>Prompt *</InputLabel>
                  <Select
                    name='prompt_id'
                    value={formData.prompt_id}
                    onChange={handleInputChange}
                    label='Prompt *'
                    required
                    disabled={!formData.project_id}
                  >
                    {prompts.map((prompt) => (
                      <MenuItem key={prompt.id} value={prompt.id}>
                        {prompt.type} Prompt
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        )}

        {activeStep === 1 && (
          <Box>
            <Typography variant='h6' gutterBottom>
              Upload Contact Data
            </Typography>

            <Paper
              variant='outlined'
              sx={{
                p: 3,
                mb: 3,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                border: '2px dashed #ccc',
                borderRadius: 2,
                cursor: 'pointer',
              }}
              onClick={() => document.getElementById('file-upload').click()}
            >
              <input
                id='file-upload'
                type='file'
                accept='.csv'
                style={{ display: 'none' }}
                onChange={handleFileChange}
              />
              <CloudUploadIcon fontSize='large' color='primary' sx={{ mb: 2 }} />
              <Typography variant='h6' gutterBottom>
                {file ? file.name : 'Upload CSV File'}
              </Typography>
              <Typography variant='body2' color='text.secondary' align='center'>
                Click to browse or drag and drop your file here
              </Typography>
              <Typography variant='caption' color='text.secondary' sx={{ mt: 1 }}>
                Required format: CSV file with columns for phone number & name
              </Typography>
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
              <Button onClick={downloadTemplate} startIcon={<DownloadIcon />} size='small'>
                Download Template
              </Button>
            </Box>

            {file && previewData.length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant='subtitle1' gutterBottom>
                  Data Preview (first 5 rows):
                </Typography>
                <TableContainer component={Paper} variant='outlined'>
                  <Table size='small'>
                    <TableHead>
                      <TableRow>
                        {Object.keys(previewData[0]).map((header) => (
                          <TableCell key={header}>{header}</TableCell>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {previewData.map((row, index) => (
                        <TableRow key={index}>
                          {Object.keys(row).map((key) => (
                            <TableCell key={key}>{row[key]}</TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <Typography
                  variant='caption'
                  color='text.secondary'
                  sx={{ mt: 1, display: 'block' }}
                >
                  Total Records: {previewData.length}+
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {activeStep === 2 && (
          <Box>
            <Typography variant='h6' gutterBottom>
              Review and Confirm
            </Typography>

            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6}>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    Batch Name
                  </Typography>
                  <Typography variant='body1'>{formData.batch_name}</Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    Project
                  </Typography>
                  <Typography variant='body1'>
                    {projects.find((p) => p.id === formData.project_id)?.name || ''}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    Persona
                  </Typography>
                  <Typography variant='body1'>
                    {personas.find((p) => p.id === formData.persona_id)?.name || ''}
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    Prompt
                  </Typography>
                  <Typography variant='body1'>
                    {prompts.find((p) => p.id === formData.prompt_id)?.type || ''} Prompt
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12}>
                <Paper variant='outlined' sx={{ p: 2 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    File
                  </Typography>
                  <Typography variant='body1'>
                    {file?.name || ''} - {previewData.length}+ records
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            <Alert severity='info' sx={{ mb: 3 }}>
              Once triggered, calls will be queued and processed. You can monitor progress in the
              Call Logs section.
            </Alert>
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button disabled={activeStep === 0} onClick={handleBack}>
            Back
          </Button>

          <Box>
            {activeStep === steps.length - 1 ? (
              <Button
                variant='contained'
                color='primary'
                startIcon={<PlayArrowIcon />}
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? 'Processing...' : 'Trigger Batch'}
                {loading && <CircularProgress size={20} sx={{ ml: 1 }} />}
              </Button>
            ) : (
              <Button variant='contained' color='primary' onClick={handleNext}>
                Next
              </Button>
            )}
          </Box>
        </Box>
      </Paper>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onClose={handleClose} maxWidth='sm' fullWidth>
        <DialogTitle sx={{ display: 'flex', alignItems: 'center' }}>
          <CheckIcon sx={{ color: 'success.main', mr: 1 }} />
          Batch Created Successfully
          <IconButton
            aria-label='close'
            onClick={handleClose}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography variant='body1' gutterBottom>
            Your batch call has been created and queued for processing.
          </Typography>
          <Typography variant='body2' gutterBottom>
            Batch ID: <strong>{batchId}</strong>
          </Typography>
          <Typography variant='body2' color='text.secondary'>
            You can monitor the progress and results in the Call Logs section.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
          <Button variant='contained' color='primary' onClick={handleViewCallLogs}>
            View Call Logs
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BatchCallTrigger;

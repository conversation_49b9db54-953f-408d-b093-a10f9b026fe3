import React from 'react';
import DataTable from '../../components/DataTable/DataTable';

const CallResponseViewer = () => {
  // Placeholder data
  const responses = [
    { phone: '9876543210', q1: 'Yes', q2: 'No', date: '2025-05-17' },
    { phone: '9123456789', q1: 'No', q2: 'Yes', date: '2025-05-16' },
  ];

  return (
    <div>
      <h2>Call Response Viewer</h2>
      <div>Filters (phone, date)</div>
      <DataTable
        data={responses}
        columns={[
          { Header: 'Phone', accessor: 'phone' },
          { Header: 'Q1', accessor: 'q1' },
          { Header: 'Q2', accessor: 'q2' },
          { Header: 'Date', accessor: 'date' },
        ]}
      />
      <button>Export as CSV</button>
      <button>Export as JSON</button>
    </div>
  );
};
export default CallResponseViewer;

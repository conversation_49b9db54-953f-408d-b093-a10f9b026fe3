import React from 'react';
import { Box, Typography, Card, Grid, Divider, Chip, Stack, Paper } from '@mui/material';
import { useProject } from '../../contexts/ProjectContext';
import ProjectDetail from '../ProjectDetail';
import LanguageIcon from '@mui/icons-material/Language';
import LocationOnIcon from '@mui/icons-material/LocationOn';

const ProjectInfoCard = ({ project }) => {
  return (
    <Paper elevation={0} sx={{ p: 3, mb: 3, backgroundColor: 'background.default' }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant='h5' gutterBottom>
            {project.title}
          </Typography>
          <Stack direction='row' spacing={1} alignItems='center' sx={{ mb: 2 }}>
            <Chip
              label={project.status}
              color={project.status === 'Active' ? 'success' : 'default'}
              size='small'
            />
            <Typography variant='body2' color='text.secondary'>
              Project ID: {project.id}
            </Typography>
          </Stack>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LanguageIcon color='action' />
              <Box>
                <Typography variant='subtitle2' color='text.secondary'>
                  Language
                </Typography>
                <Typography variant='body1'>{project.language || 'Not specified'}</Typography>
              </Box>
            </Box>
          </Stack>
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LocationOnIcon color='action' />
              <Box>
                <Typography variant='subtitle2' color='text.secondary'>
                  Region
                </Typography>
                <Typography variant='body1'>{project.region || ''}</Typography>
              </Box>
            </Box>
          </Stack>
        </Grid>
      </Grid>
    </Paper>
  );
};

const Configuration = () => {
  const { selectedProject, loading, error } = useProject();
  console.log(selectedProject);

  return (
    <Box sx={{ maxWidth: 1200, margin: '0 auto', p: 3 }}>
      <Typography variant='h4' component='h1' gutterBottom>
        Configuration
      </Typography>
      <Typography variant='subtitle1' color='text.secondary' gutterBottom>
        Manage system settings and configurations
      </Typography>

      <Card sx={{ mt: 3, mb: 4, p: 3 }}>
        {error && (
          <Typography color='error' sx={{ mb: 2 }}>
            {error}
          </Typography>
        )}

        {loading ? (
          <Typography>Loading projects...</Typography>
        ) : !selectedProject ? (
          <Typography>
            No projects found. Please select a project from the dropdown above.
          </Typography>
        ) : (
          <>
            <ProjectInfoCard project={selectedProject} />
            <Divider sx={{ my: 1 }} />
            <ProjectDetail projectId={selectedProject.id} />
          </>
        )}
      </Card>
    </Box>
  );
};

export default Configuration;

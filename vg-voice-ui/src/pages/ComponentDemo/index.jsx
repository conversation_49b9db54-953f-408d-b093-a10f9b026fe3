import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Phone,
  SmartToy,
  Speed,
  AccessTime,
  VolumeUp,
  Analytics,
} from '@mui/icons-material';

// Import our custom components
import FuturisticStatCard from '../../components/FuturisticStatCard';
import WaveformVisualizer from '../../components/WaveformVisualizer';
import AIStatusIndicator from '../../components/AIStatusIndicator';

/**
 * Component Demo Page
 * Showcases all the futuristic AI components
 */
const ComponentDemo = () => {
  const [waveformActive, setWaveformActive] = useState(false);
  const [waveformStyle, setWaveformStyle] = useState('bars');
  const [statusIndicator, setStatusIndicator] = useState('idle');
  const [cardVariant, setCardVariant] = useState('primary');

  const statusOptions = [
    'idle',
    'calling',
    'connected',
    'ai_processing',
    'speaking',
    'completed',
    'failed',
  ];

  const cardVariants = ['default', 'primary', 'success', 'warning', 'error', 'gradient'];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography 
        variant="h3" 
        sx={{ 
          fontWeight: 700,
          background: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          mb: 2,
          textAlign: 'center',
        }}
      >
        AI Dashboard Components Demo
      </Typography>
      
      <Typography 
        variant="h6" 
        sx={{ 
          color: 'text.secondary',
          fontWeight: 400,
          textAlign: 'center',
          mb: 6,
        }}
      >
        Interactive showcase of futuristic UI components
      </Typography>

      {/* Controls */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          Interactive Controls
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={waveformActive}
                  onChange={(e) => setWaveformActive(e.target.checked)}
                />
              }
              label="Waveform Active"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Waveform Style</InputLabel>
              <Select
                value={waveformStyle}
                label="Waveform Style"
                onChange={(e) => setWaveformStyle(e.target.value)}
              >
                <MenuItem value="bars">Bars</MenuItem>
                <MenuItem value="wave">Wave</MenuItem>
                <MenuItem value="circular">Circular</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={statusIndicator}
                label="Status"
                onChange={(e) => setStatusIndicator(e.target.value)}
              >
                {statusOptions.map((status) => (
                  <MenuItem key={status} value={status}>
                    {status.replace('_', ' ').toUpperCase()}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Card Variant</InputLabel>
              <Select
                value={cardVariant}
                label="Card Variant"
                onChange={(e) => setCardVariant(e.target.value)}
              >
                {cardVariants.map((variant) => (
                  <MenuItem key={variant} value={variant}>
                    {variant.toUpperCase()}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* AI Status Indicators */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          AI Status Indicators
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Current Status: {statusIndicator}
            </Typography>
            <AIStatusIndicator 
              status={statusIndicator}
              animated={true}
              showChip={false}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              All Status Types
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {statusOptions.map((status) => (
                <AIStatusIndicator 
                  key={status}
                  status={status}
                  animated={status === statusIndicator}
                  showText={false}
                  size="small"
                />
              ))}
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Waveform Visualizers */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          Waveform Visualizers
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Bars Style
            </Typography>
            <WaveformVisualizer
              isActive={waveformActive}
              style="bars"
              height={120}
              color="primary"
            />
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Wave Style
            </Typography>
            <WaveformVisualizer
              isActive={waveformActive}
              style="wave"
              height={120}
              color="secondary"
            />
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Circular Style
            </Typography>
            <WaveformVisualizer
              isActive={waveformActive}
              style="circular"
              height={120}
              color="success"
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Futuristic Stat Cards */}
      <Paper sx={{ p: 3, mb: 4, borderRadius: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          Futuristic Stat Cards
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <FuturisticStatCard
              title="Total Calls"
              value="1,247"
              subtitle="Today's activity"
              icon={<Phone />}
              variant={cardVariant}
              trend="up"
              trendValue="+12.5%"
              animated
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FuturisticStatCard
              title="AI Processing"
              value="8"
              subtitle="Active conversations"
              icon={<SmartToy />}
              variant="success"
              progress={75}
              progressColor="success"
              animated
              glowing={waveformActive}
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FuturisticStatCard
              title="Success Rate"
              value="94.2%"
              subtitle="Completion rate"
              icon={<Speed />}
              variant="gradient"
              trend="up"
              trendValue="****%"
              animated
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FuturisticStatCard
              title="Avg Duration"
              value="3m 5s"
              subtitle="Per conversation"
              icon={<AccessTime />}
              variant="warning"
              chip={{
                label: "Optimized",
                color: "success",
                variant: "outlined"
              }}
              animated
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Interactive Demo */}
      <Paper sx={{ p: 3, borderRadius: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          Interactive Demo
        </Typography>
        
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Button
            variant="contained"
            size="large"
            onClick={() => {
              setWaveformActive(!waveformActive);
              setStatusIndicator(waveformActive ? 'idle' : 'ai_processing');
            }}
            sx={{
              borderRadius: 3,
              px: 4,
              py: 1.5,
              background: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #4B0082 0%, #00BFFF 100%)',
              },
            }}
          >
            {waveformActive ? 'Stop Demo' : 'Start Demo'}
          </Button>
        </Box>
        
        <Typography variant="body1" sx={{ textAlign: 'center', color: 'text.secondary' }}>
          Click the button above to see the components in action!
        </Typography>
      </Paper>
    </Container>
  );
};

export default ComponentDemo;

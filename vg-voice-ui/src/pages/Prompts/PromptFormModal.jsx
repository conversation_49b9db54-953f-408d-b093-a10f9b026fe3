import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import FormBuilder from '../../components/form/FormBuilder';

const PromptType = {
  CONVERSATIONAL: 'Conversational',
  SUMMARY: 'Summary',
  FALLBACK: 'Fallback',
};

const PromptStatus = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  TESTING: 'Testing',
  INACTIVE: 'Inactive',
};

const PromptModel = {
  GPT_4: 'gpt-4',
  GPT_3_5: 'gpt-3.5-turbo',
  CLAUDE_2: 'claude-2',
  LLAMA_2: 'llama-2',
  GEMINI_PRO: 'gemini-pro',
};

const getFields = () => [
  {
    name: 'type',
    label: 'Type',
    type: 'select',
    required: true,
    options: Object.entries(PromptType).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
  },
  {
    name: 'prompt_text',
    label: 'Prompt Text',
    type: 'textarea',
    required: true,
    gridProps: { xs: 12 },
    size: 'large',
    rows: 6,
    multiline: true,
    minRows: 4,
    maxRows: 8,
    sx: {
      '& .MuiInputBase-root': {
        fontFamily: 'monospace',
        fontSize: '0.875rem',
      },
    },
  },
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    required: true,
    options: Object.entries(PromptStatus).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
  },
  {
    name: 'model',
    label: 'Model',
    type: 'select',
    required: true,
    options: Object.entries(PromptModel).map(([key, value]) => ({ label: value, value })),
    gridProps: { xs: 12 },
    size: 'large',
  },
];

const PromptFormModal = ({ open, onClose, onSubmit, initialValues, mode }) => {
  const theme = useTheme();
  const fields = getFields();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          py: 2,
          fontSize: '1.25rem',
          fontWeight: 500,
        }}
      >
        {mode === 'edit' ? 'Edit Prompt' : 'Add New Prompt'}
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 3 }}>
        <FormBuilder
          fields={fields}
          initialValues={initialValues}
          onSubmit={onSubmit}
          formLayout={{
            fieldSpacing: 3,
            direction: 'column',
            variant: 'standard',
            padding: 0,
            buttonAlignment: 'space-between',
            submitButtonProps: {
              variant: 'contained',
              color: 'primary',
              size: 'large',
              sx: { px: 4 },
            },
            cancelButtonProps: {
              variant: 'outlined',
              color: 'inherit',
              size: 'large',
              sx: { px: 4 },
            },
          }}
          submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
          cancelButtonText='Cancel'
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default PromptFormModal;

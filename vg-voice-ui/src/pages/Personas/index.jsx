import React, { useEffect, useState, useCallback } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import {
  listPersonas,
  createPersona,
  updatePersona,
  deletePersona,
  getPersonaVersions,
  createPersonaVersion,
  updatePersonaVersion,
} from '../../services/personaService';
import PersonaFormModal from './PersonaFormModal';
import { useNavigate } from 'react-router-dom';
import { Typography, Link } from '@mui/material';
import VersionsDialog from '../../components/VersionsDialog/VersionsDialog';

const getColumnsBase = (handleOpenVersionsDialog) => [
  {
    headerName: 'Name',
    field: 'name',
    renderCell: ({ row, value }) => (
      <Link
        component='button'
        variant='body2'
        onClick={(e) => {
          e.stopPropagation();
          handleOpenVersionsDialog(row);
          return false;
        }}
      >
        {value}
      </Link>
    ),
  },
  {
    headerName: 'Voice Type',
    field: 'voice_type',
    renderCell: ({ row }) => row.current_version?.voice_type || row.voice_type || '-',
  },
  {
    headerName: 'Engine',
    field: 'engine',
    renderCell: ({ row }) => row.current_version?.engine || row.engine || '-',
  },
  {
    headerName: 'Language',
    field: 'language',
    renderCell: ({ row }) => row.current_version?.language || row.language || '-',
  },
  {
    headerName: 'Phone Number',
    field: 'from_number',
    renderCell: ({ row }) => row.current_version?.from_number || row.from_number || '-',
  },
  {
    headerName: 'Default',
    field: 'is_default',
    renderCell: ({ value }) => (value ? 'Yes' : 'No'),
  },
];

const Personas = ({ projectId, inProjectDetail = false, setError }) => {
  const navigate = useNavigate();
  const [personas, setPersonas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    name: '',
    voice_type: '',
    engine: '',
    language: '',
    from_number: '',
    is_default: false,
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  // Versions dialog state
  const [versionsDialogOpen, setVersionsDialogOpen] = useState(false);
  const [selectedPersona, setSelectedPersona] = useState(null);
  const [personaVersions, setPersonaVersions] = useState([]);

  const fetchPersonas = useCallback(
    async (page = 0, limit = 10) => {
      setLoading(true);
      setError(null);
      try {
        const params = { skip: page * limit, limit };

        if (projectId) {
          params.project_id = projectId;
        }

        const res = await listPersonas(params);
        setPersonas(res.items || []);
        setTotalCount(res.total ?? 0);
      } catch (err) {
        console.error('Error loading personas:', err);
        setError('Failed to load personas');
      } finally {
        setLoading(false);
      }
    },
    [projectId]
  );

  useEffect(() => {
    fetchPersonas(page, rowsPerPage);
  }, [fetchPersonas, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalMode('create');
    setModalInitialValues({
      name: '',
      voice_type: '',
      engine: '',
      language: '',
      from_number: '',
      is_default: false,
    });
    setModalOpen(true);
    setActionError(null);
  };

  // EDIT
  const handleOpenEdit = (persona) => {
    setModalMode('edit');
    setModalInitialValues({
      id: persona.id,
      name: persona.name,
      status: persona.status || 'Draft',
      is_default: persona.is_default || false,
    });
    setModalOpen(true);
    setActionError(null);
  };

  const handleDelete = async (personaId) => {
    if (!window.confirm('Are you sure you want to delete this persona?')) return;
    setActionLoading(true);
    setActionError(null);
    try {
      await deletePersona(personaId);
      fetchPersonas(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to delete persona');
    } finally {
      setActionLoading(false);
    }
  };

  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'edit') {
        const changedFields = {};

        changedFields.id = values.id;

        Object.keys(values).forEach((key) => {
          if (JSON.stringify(values[key]) !== JSON.stringify(modalInitialValues[key])) {
            changedFields[key] = values[key];
          }
        });

        await updatePersona(values.id, changedFields);
      } else {
        const personaData = projectId ? { ...values, project_id: projectId } : values;
        await createPersona(personaData);
      }
      setModalOpen(false);
      fetchPersonas(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to save persona');
    } finally {
      setActionLoading(false);
    }
  };

  const handleOpenVersionsDialog = (persona) => {
    setSelectedPersona(persona);
    setVersionsDialogOpen(true);
    fetchPersonaVersions(persona.id);
  };

  const fetchPersonaVersions = async (personaId) => {
    setActionLoading(true);
    setActionError(null);
    try {
      const response = await getPersonaVersions(personaId);
      setPersonaVersions(response || []);
    } catch (err) {
      console.error('Error loading persona versions:', err);
      setActionError('Failed to load persona versions');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCreatePersonaVersion = async (personaId, versionData) => {
    setActionLoading(true);
    try {
      await createPersonaVersion(personaId, versionData);
      if (selectedPersona) {
        await fetchPersonaVersions(selectedPersona.id);
        fetchPersonas(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error creating persona version:', err);
      setActionError('Failed to create persona version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpdatePersonaVersion = async (versionId, versionData) => {
    setActionLoading(true);
    try {
      await updatePersonaVersion(versionId, versionData);
      if (selectedPersona) {
        await fetchPersonaVersions(selectedPersona.id);
        fetchPersonas(page, rowsPerPage);
      }
      return true;
    } catch (err) {
      console.error('Error updating persona version:', err);
      setActionError('Failed to update persona version');
      return false;
    } finally {
      setActionLoading(false);
    }
  };

  const getColumns = () => {
    const columnsBase = getColumnsBase(handleOpenVersionsDialog);

    if (inProjectDetail) {
      return [
        ...columnsBase,
        {
          headerName: 'Actions',
          field: 'actions',
          renderCell: ({ row }) => (
            <div style={{ display: 'flex', gap: 8 }}>
              <Button size='small' variant='outlined' onClick={() => handleOpenEdit(row)}>
                Edit
              </Button>
            </div>
          ),
        },
      ];
    }

    return [
      ...columnsBase,
      {
        headerName: 'Actions',
        field: 'actions',
        renderCell: ({ row }) => (
          <div style={{ display: 'flex', gap: 8 }}>
            <Button size='small' variant='outlined' onClick={() => handleOpenEdit(row)}>
              Edit
            </Button>
            <Button
              size='small'
              variant='outlined'
              color='error'
              onClick={() => handleDelete(row.id)}
              disabled={actionLoading}
            >
              Delete
            </Button>
          </div>
        ),
      },
    ];
  };

  const columns = getColumns();

  return (
    <div>
      {!inProjectDetail && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 24,
          }}
        >
          <h2>Personas</h2>
          <Button onClick={handleOpenCreate}>+ Create Persona</Button>
        </div>
      )}
      {inProjectDetail && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <Typography variant='h6'>Personas</Typography>
          <Button variant='contained' onClick={handleOpenCreate}>
            + Add Persona
          </Button>
        </div>
      )}
      {actionError && <div style={{ color: 'red' }}>{actionError}</div>}
      <DataTable
        data={personas}
        columns={columns}
        loading={loading || actionLoading}
        totalCount={personas.length > 0 ? Math.max(totalCount, personas.length) : totalCount}
        serverSidePagination
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        emptyStateMessage={loading ? 'Loading...' : 'No personas found'}
        searchEnabled={false}
      />
      <PersonaFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
      />

      {/* Versions Dialog */}
      {selectedPersona && (
        <VersionsDialog
          open={versionsDialogOpen}
          onClose={() => setVersionsDialogOpen(false)}
          versions={personaVersions}
          entity={selectedPersona}
          fetchVersions={fetchPersonaVersions}
          createVersion={handleCreatePersonaVersion}
          updateVersion={handleUpdatePersonaVersion}
          fields={[
            {
              name: 'voice_type',
              label: 'Voice Type',
              type: 'select',
              required: true,
              options: [
                { value: 'Puck', label: 'Puck' },
                { value: 'Charon', label: 'Charon' },
                { value: 'Kore', label: 'Kore' },
                { value: 'Custom', label: 'Custom' },
              ],
            },
            {
              name: 'engine',
              label: 'Engine',
              type: 'select',
              required: true,
              options: [
                { value: 'Custom', label: 'Custom' },
                { value: 'Bland', label: 'Bland' },
              ],
            },
            {
              name: 'language',
              label: 'Language',
              type: 'select',
              required: true,
              options: [
                { value: 'English', label: 'English' },
                { value: 'Hindi', label: 'Hindi' },
                { value: 'Kannada', label: 'Kannada' },
                { value: 'Tamil', label: 'Tamil' },
                { value: 'Telugu', label: 'Telugu' },
              ],
            },
            { name: 'from_number', label: 'Phone Number', type: 'text', required: true },
            {
              name: 'status',
              label: 'Status',
              type: 'select',
              required: true,
              options: [
                { value: 'Active', label: 'Active' },
                { value: 'Inactive', label: 'Inactive' },
              ],
            },
            { name: 'change_notes', label: 'Change Notes', type: 'textarea', required: true },
          ]}
          entityType='persona'
        />
      )}
    </div>
  );
};

export default Personas;

import React, { useState } from 'react';

const tabList = [
  { label: 'Personas', value: 'personas' },
  { label: 'Prompts', value: 'prompts' },
];

const PersonaPromptManager = () => {
  const [tab, setTab] = useState('personas');
  return (
    <div>
      <h2>Persona & Prompt Manager</h2>
      <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
        {tabList.map((t) => (
          <button
            key={t.value}
            style={{ fontWeight: tab === t.value ? 'bold' : 'normal' }}
            onClick={() => setTab(t.value)}
          >
            {t.label}
          </button>
        ))}
      </div>
      <div>
        {tab === 'personas' && <div>Persona Config Form & Version History</div>}
        {tab === 'prompts' && <div>Prompt Config Form & Version History</div>}
      </div>
    </div>
  );
};
export default PersonaPromptManager;

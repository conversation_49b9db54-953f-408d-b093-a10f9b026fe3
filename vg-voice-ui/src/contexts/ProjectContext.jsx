import React, { createContext, useContext, useState, useEffect } from 'react';
import { listProjects } from '../services/projectsService';

const SELECTED_PROJECT_KEY = 'selectedProjectId';

const ProjectContext = createContext();

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};

export const ProjectProvider = ({ children }) => {
  const [projects, setProjects] = useState([]);
  const [selectedProjectId, setSelectedProjectId] = useState(() => {
    return localStorage.getItem(SELECTED_PROJECT_KEY) || '';
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const updateSelectedProjectId = (id) => {
    setSelectedProjectId(id);
    localStorage.setItem(SELECTED_PROJECT_KEY, id);
  };

  // Fetch projects function that can be called manually
  const fetchProjects = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await listProjects();
      const projectItems = response.items || [];
      setProjects(projectItems);

      // If we have a selectedProjectId but no matching project, and there are projects available,
      // select the first project
      if (
        selectedProjectId &&
        projectItems.length > 0 &&
        !projectItems.find((p) => p.id === selectedProjectId)
      ) {
        updateSelectedProjectId(projectItems[0].id);
      }
      // If no project is selected and there are projects available, select the first one
      else if (!selectedProjectId && projectItems.length > 0) {
        updateSelectedProjectId(projectItems[0].id);
      }
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects when the component mounts
  useEffect(() => {
    fetchProjects();
  }, []);

  // Find the selected project from the projects array
  const selectedProject = projects.find((p) => p.id === selectedProjectId);

  const value = {
    projects,
    selectedProjectId,
    selectedProject,
    loading,
    error,
    setSelectedProjectId: updateSelectedProjectId,
    refreshProjects: fetchProjects,
  };

  return <ProjectContext.Provider value={value}>{children}</ProjectContext.Provider>;
};

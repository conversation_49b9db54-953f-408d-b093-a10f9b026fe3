# Layout and Navigation System

This document provides instructions on how to use the Layout system and how to add new modules to the sidebar navigation menu.

## Overview

The Layout system consists of three main components:

1. **Layout** (`Layout.jsx`) - The main wrapper component that includes the AppHeader and Sidebar
2. **AppHeader** (`AppHeader.jsx`) - The top navigation bar with hamburger menu and user profile
3. **Sidebar** (`Sidebar.jsx`) - The side navigation menu with support for nested menu items

## How to Add a New Module to the Sidebar Navigation

### 1. Edit the Menu Items Configuration

In the `Sidebar.jsx` file, find the `menuItems` array. This array contains the configuration for all menu items in the sidebar.

```javascript
const menuItems = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    path: '/dashboard',
    icon: <DashboardIcon />
  },
  // ... more menu items
];
```

### 2. Add a New Top-Level Menu Item

To add a new top-level menu item, add an object to the `menuItems` array with the following properties:

```javascript
{
  id: 'yourModuleId',      // Unique identifier for the menu item
  title: 'Your Module',    // Display name shown in the menu
  path: '/your-module',    // URL path for this module
  icon: <YourModuleIcon /> // Material-UI icon component
}
```

Example:

```javascript
{
  id: 'analytics',
  title: 'Analytics',
  path: '/analytics',
  icon: <BarChartIcon />
}
```

### 3. Add a Menu Item with Nested Children

To add a menu item with nested children (dropdown), use the following structure:

```javascript
{
  id: 'parentModuleId',
  title: 'Parent Module',
  icon: <ParentModuleIcon />,
  children: [
    {
      id: 'childModule1',
      title: 'Child Module 1',
      path: '/parent-module/child-1',
    },
    {
      id: 'childModule2',
      title: 'Child Module 2',
      path: '/parent-module/child-2',
    }
  ]
}
```

Example:

```javascript
{
  id: 'reports',
  title: 'Reports',
  icon: <AssessmentIcon />,
  children: [
    {
      id: 'salesReports',
      title: 'Sales Reports',
      path: '/reports/sales',
    },
    {
      id: 'inventoryReports',
      title: 'Inventory Reports',
      path: '/reports/inventory',
    }
  ]
}
```

## How to Map Routes to Components

After adding menu items to the sidebar, you need to map those routes to actual components in your application.

### 1. Create Your Component

First, create your component in the appropriate directory (typically under `/src/pages/`).

Example:
```jsx
// src/pages/Analytics/index.jsx
import React from 'react';

const AnalyticsPage = () => {
  return (
    <div>
      <h1>Analytics</h1>
      <p>This is the analytics page.</p>
    </div>
  );
};

export default AnalyticsPage;
```

### 2. Import Your Component in App.js

In your `App.js` file, import the new component:

```jsx
import AnalyticsPage from './pages/Analytics';
```

### 3. Add a New Route in App.js

Add a new route in the Routes section of App.js:

```jsx
<Route path="/analytics" element={
  <AuthGuard>
    <Layout>
      <AnalyticsPage />
    </Layout>
  </AuthGuard>
} />
```

### 4. For Nested Routes

For nested routes, you can use nested Route components:

```jsx
<Route path="/reports" element={
  <AuthGuard>
    <Layout>
      <Routes>
        <Route path="/sales" element={<SalesReportsPage />} />
        <Route path="/inventory" element={<InventoryReportsPage />} />
        <Route index element={<Navigate to="/reports/sales" replace />} />
      </Routes>
    </Layout>
  </AuthGuard>
} />
```

Or use separate route definitions:

```jsx
<Route path="/reports/sales" element={
  <AuthGuard>
    <Layout>
      <SalesReportsPage />
    </Layout>
  </AuthGuard>
} />

<Route path="/reports/inventory" element={
  <AuthGuard>
    <Layout>
      <InventoryReportsPage />
    </Layout>
  </AuthGuard>
} />
```

## Icons

For icons, you can import from Material-UI's icon set:

```javascript
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import BarChartIcon from '@mui/icons-material/BarChart';
// etc.
```

## Best Practices

1. Use clear, descriptive IDs for your menu items
2. Keep related functionality grouped together
3. Use consistent naming between menu items and routes
4. For nested menus, limit to one level of nesting for better UX
5. Consider the mobile experience - too many menu items can be overwhelming

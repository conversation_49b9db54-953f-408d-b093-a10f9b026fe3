import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Divider,
  Box,
  Typography,
  styled,
  useTheme,
} from '@mui/material';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  Phone as PhoneIcon,
  History as HistoryIcon,
  Business as BusinessIcon,
  SmartToy as AIIcon,
  Science as DemoIcon,
} from '@mui/icons-material';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';

// Styled component for the drawer
// const StyledDrawer = styled(Drawer)(({ theme, variant }) => ({
//   width: 240,
//   flexShrink: 0,
//   '& .MuiDrawer-paper': {
//     width: 240,
//     boxSizing: 'border-box',
//     top: 64, // Height of AppBar
//     height: 'calc(100% - 64px)',
//     [theme.breakpoints.down('sm')]: {
//       top: 56, // AppBar height on mobile
//       height: 'calc(100% - 56px)',
//     },
//   },
//   // Make temporary drawer different from permanent
//   ...(variant === 'temporary' && {
//     '& .MuiDrawer-paper': {
//       position: 'fixed',
//       zIndex: theme.zIndex.drawer,
//     },
//   }),
// }));

// Styled component for active menu items
const ActiveListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  backgroundColor: active ? theme.palette.action.selected : 'transparent',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

/**
 * Navigation menu structure with nested options
 * This can be expanded as needed with more menu options
 */
// Define menu items for navigation

const menuItems = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: <DashboardIcon />,
  },
  {
    title: 'AI Dashboard',
    path: '/ai-dashboard',
    icon: <AIIcon />,
  },
  {
    title: 'Component Demo',
    path: '/component-demo',
    icon: <DemoIcon />,
  },
  {
    title: 'Projects',
    path: '/projects',
    icon: <BusinessIcon />,
  },
  {
    title: 'Call Trigger',
    path: '/call-trigger',
    icon: <PhoneIcon />,
  },
  {
    title: 'Call Logs',
    path: '/call-logs',
    icon: <HistoryIcon />,
  },
  {
    id: 'analytics',
    title: 'Analytics',
    path: '/analytics',
    icon: <MonetizationOnIcon />,
  },
  {
    id: 'configuration',
    title: 'Configuration',
    path: '/configuration',
    icon: <DashboardIcon />,
  },
];

/**
 * Sidebar component with navigation menu
 * Collapsible drawer that can be toggled with hamburger menu
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether sidebar is open
 * @param {Function} props.handleDrawerToggle - Function to toggle sidebar
 * @param {string} props.variant - Drawer variant (temporary|persistent|permanent)
 * @returns {JSX.Element} Sidebar component
 */
const Sidebar = ({ open, handleDrawerToggle, variant = 'persistent' }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState({});

  // Check if a path is active
  const isActivePath = (path) => {
    return location.pathname === path;
  };

  // Check if a parent menu has an active child
  const hasActiveChild = (children) => {
    return children?.some((child) => isActivePath(child.path));
  };

  // Toggle menu expansion
  const handleToggleMenu = (id) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Set up event handler for list item clicks (to close drawer on mobile)
  const handleMenuItemClick = (path) => {
    try {
      navigate(path);
      if (variant === 'temporary') {
        handleDrawerToggle(); // Close drawer on mobile when item is clicked
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  // Recursive function to render menu items
  const renderMenuItem = (item) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActivePath(item.path);
    const isExpanded = expandedMenus[item.id] || hasActiveChild(item.children);

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding>
          <ActiveListItemButton
            active={isActive ? 1 : 0}
            component={item.path && !hasChildren ? Link : undefined}
            to={item.path && !hasChildren ? item.path : undefined}
            onClick={() => {
              if (hasChildren) {
                handleToggleMenu(item.id);
              } else if (item.path) {
                // Use handleMenuItemClick instead of handleNavigate to properly close drawer on mobile
                handleMenuItemClick(item.path);
              }
            }}
            sx={{ pl: 2 }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.title} />
            {hasChildren && (isExpanded ? <ExpandLess /> : <ExpandMore />)}
          </ActiveListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout='auto' unmountOnExit>
            <List component='div' disablePadding>
              {item.children.map((child) => (
                <ListItem disablePadding key={child.id}>
                  <ActiveListItemButton
                    active={isActivePath(child.path) ? 1 : 0}
                    onClick={() => handleMenuItemClick(child.path)}
                    component={Link}
                    to={child.path}
                    sx={{ pl: 4 }}
                  >
                    <ListItemText primary={child.title} />
                  </ActiveListItemButton>
                </ListItem>
              ))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <>
      <Box sx={{ p: 2 }}>
        <Typography variant='subtitle1' sx={{ fontWeight: 'bold' }}>
          Menu
        </Typography>
      </Box>
      <Divider />
      <List component='nav'>{menuItems.map(renderMenuItem)}</List>
      {/* This empty box will push the white background to full height */}
      <Box sx={{ flexGrow: 1, bgcolor: 'background.paper', minHeight: '70vh' }} />
    </>
  );

  // For temporary drawer (mobile), we need different props than for persistent/permanent
  const drawerProps =
    variant === 'temporary'
      ? {
          // For mobile: Use temporary variant that fully overlays content
          variant: 'temporary',
          anchor: 'left',
          open: open,
          onClose: handleDrawerToggle,
          ModalProps: {
            keepMounted: true, // Better mobile performance
          },
          sx: {
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 240,
              boxShadow: 3,
            },
          },
        }
      : {
          // For desktop: Use persistent variant that can be toggled
          variant: 'persistent',
          anchor: 'left',
          open: open,
          sx: {
            width: 240,
            flexShrink: 0,
            [`& .MuiDrawer-paper`]: {
              width: 240,
              boxSizing: 'border-box',
            },
          },
        };

  return (
    <Drawer
      {...drawerProps}
      sx={{
        width: 240,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
          top: 64, // AppBar height
          height: 'calc(100% - 64px)',
          [theme.breakpoints.down('sm')]: {
            top: 56, // AppBar height on mobile
            height: 'calc(100% - 56px)',
          },
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;

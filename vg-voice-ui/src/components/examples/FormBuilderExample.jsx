import React, { useState } from 'react';
import { Container, Paper, Typography, Box, Grid, Snackbar, Alert, Tab, Tabs } from '@mui/material';
import FormBuilder from '../form/FormBuilder';

/**
 * Example component to demonstrate FormBuilder usage
 */
const FormBuilderExample = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Show notification
  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // -- BASIC USER FORM --
  const userFormFields = [
    {
      name: 'firstName',
      label: 'First Name',
      type: 'text',
      validations: ['required'],
      helperText: 'Enter your first name',
      gridProps: { md: 6 },
    },
    {
      name: 'lastName',
      label: 'Last Name',
      type: 'text',
      validations: ['required'],
      helperText: 'Enter your last name',
      gridProps: { md: 6 },
    },
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      validations: ['email'],
      helperText: 'Enter a valid email address',
      gridProps: { md: 6 },
    },
    {
      name: 'phoneNumber',
      label: 'Phone Number',
      type: 'tel',
      validations: ['phone'],
      helperText: 'Enter a 10 digit phone number',
      gridProps: { md: 6 },
    },
    {
      name: 'dateOfBirth',
      label: 'Date of Birth',
      type: 'date',
      validations: ['pastDate'],
      helperText: 'Select your date of birth',
      gridProps: { md: 6 },
    },
    {
      name: 'role',
      label: 'Role',
      type: 'select',
      options: [
        { value: 'admin', label: 'Administrator' },
        { value: 'manager', label: 'Manager' },
        { value: 'user', label: 'Regular User' },
      ],
      validations: ['required'],
      helperText: 'Select your role',
      gridProps: { md: 6 },
    },
    {
      name: 'agreeToTerms',
      label: 'I agree to the terms and conditions',
      type: 'checkbox',
      validations: ['accepted'],
      gridProps: { md: 12 },
    },
  ];

  // -- CONDITIONAL FORM --
  const conditionalFormFields = [
    {
      name: 'contactMethod',
      label: 'How would you like to be contacted?',
      type: 'radio',
      options: [
        { value: 'email', label: 'Email' },
        { value: 'phone', label: 'Phone' },
        { value: 'both', label: 'Both' },
      ],
      validations: ['required'],
      row: true,
      gridProps: { md: 12 },
    },
    {
      name: 'email',
      label: 'Email Address',
      type: 'email',
      validations: ['email'],
      helperText: 'Enter a valid email address',
      gridProps: { md: 6 },
      // Show this field if contactMethod is 'email' or 'both'
      conditional: (values) => ['email', 'both'].includes(values.contactMethod),
    },
    {
      name: 'phoneNumber',
      label: 'Phone Number',
      type: 'tel',
      validations: ['phone'],
      helperText: 'Enter a 10 digit phone number',
      gridProps: { md: 6 },
      // Show this field if contactMethod is 'phone' or 'both'
      conditional: (values) => ['phone', 'both'].includes(values.contactMethod),
    },
    {
      name: 'bestTimeToCall',
      label: 'Best Time to Call',
      type: 'select',
      options: [
        { value: 'morning', label: 'Morning (8am-12pm)' },
        { value: 'afternoon', label: 'Afternoon (12pm-5pm)' },
        { value: 'evening', label: 'Evening (5pm-9pm)' },
      ],
      helperText: 'Select your preferred time for call',
      gridProps: { md: 6 },
      // Only show this field if phone is selected as a contact method
      conditional: (values) => ['phone', 'both'].includes(values.contactMethod),
    },
    {
      name: 'newsletter',
      label: 'Subscribe to newsletter',
      type: 'checkbox',
      gridProps: { md: 12 },
      // Only show this field if email is selected as a contact method
      conditional: (values) => ['email', 'both'].includes(values.contactMethod),
    },
    {
      name: 'newsletterFrequency',
      label: 'Newsletter Frequency',
      type: 'select',
      options: [
        { value: 'daily', label: 'Daily' },
        { value: 'weekly', label: 'Weekly' },
        { value: 'monthly', label: 'Monthly' },
      ],
      helperText: 'How often would you like to receive newsletters',
      gridProps: { md: 6 },
      // Only show this field if newsletter is checked AND email is a contact method
      conditional: (values) =>
        ['email', 'both'].includes(values.contactMethod) && values.newsletter,
    },
  ];

  // -- ADVANCED FORM --
  const advancedFormFields = [
    {
      name: 'productType',
      label: 'Product Type',
      type: 'select',
      options: [
        { value: 'physical', label: 'Physical Product' },
        { value: 'digital', label: 'Digital Product' },
        { value: 'subscription', label: 'Subscription' },
      ],
      validations: ['required'],
      helperText: 'Select the type of product',
      gridProps: { md: 12 },
    },
    {
      name: 'productName',
      label: 'Product Name',
      type: 'text',
      validations: ['required'],
      helperText: 'Enter the product name',
      gridProps: { md: 6 },
    },
    {
      name: 'price',
      label: 'Price',
      type: 'number',
      validations: ['positiveNumber'],
      helperText: 'Enter the product price',
      gridProps: { md: 6 },
    },
    {
      name: 'weight',
      label: 'Weight (kg)',
      type: 'number',
      validations: ['positiveNumber'],
      helperText: 'Enter the product weight',
      gridProps: { md: 6 },
      // Only show for physical products
      conditional: (values) => values.productType === 'physical',
    },
    {
      name: 'dimensions',
      label: 'Dimensions (LxWxH cm)',
      type: 'text',
      helperText: 'Enter dimensions in the format: LxWxH',
      gridProps: { md: 6 },
      // Only show for physical products
      conditional: (values) => values.productType === 'physical',
    },
    {
      name: 'fileType',
      label: 'File Type',
      type: 'select',
      options: [
        { value: 'pdf', label: 'PDF' },
        { value: 'video', label: 'Video' },
        { value: 'audio', label: 'Audio' },
        { value: 'software', label: 'Software' },
      ],
      helperText: 'Select the type of digital file',
      gridProps: { md: 6 },
      // Only show for digital products
      conditional: (values) => values.productType === 'digital',
    },
    {
      name: 'downloadUrl',
      label: 'Download URL',
      type: 'text',
      helperText: 'Enter the download URL',
      gridProps: { md: 6 },
      // Only show for digital products
      conditional: (values) => values.productType === 'digital',
    },
    {
      name: 'billingCycle',
      label: 'Billing Cycle',
      type: 'select',
      options: [
        { value: 'monthly', label: 'Monthly' },
        { value: 'quarterly', label: 'Quarterly' },
        { value: 'annual', label: 'Annual' },
      ],
      helperText: 'Select the billing cycle',
      gridProps: { md: 6 },
      // Only show for subscription products
      conditional: (values) => values.productType === 'subscription',
    },
    {
      name: 'trialPeriod',
      label: 'Trial Period (days)',
      type: 'number',
      helperText: 'Enter the trial period in days (0 for no trial)',
      gridProps: { md: 6 },
      // Only show for subscription products
      conditional: (values) => values.productType === 'subscription',
    },
    {
      name: 'categories',
      label: 'Categories',
      type: 'multiselect',
      options: [
        { value: 'electronics', label: 'Electronics' },
        { value: 'clothing', label: 'Clothing' },
        { value: 'books', label: 'Books' },
        { value: 'home', label: 'Home & Garden' },
        { value: 'beauty', label: 'Health & Beauty' },
        { value: 'sports', label: 'Sports' },
        { value: 'toys', label: 'Toys' },
        { value: 'automotive', label: 'Automotive' },
      ],
      helperText: 'Select all applicable categories',
      gridProps: { md: 12 },
    },
    {
      name: 'productImage',
      label: 'Product Image',
      type: 'file',
      accept: 'image/*',
      helperText: 'Upload a product image (max 5MB)',
      gridProps: { md: 12 },
    },
  ];

  // Initial values for all forms
  const formInitialValues = {
    // User Form
    userForm: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      dateOfBirth: '',
      role: '',
      agreeToTerms: false,
    },
    // Conditional Form
    conditionalForm: {
      contactMethod: '',
      email: '',
      phoneNumber: '',
      bestTimeToCall: '',
      newsletter: false,
      newsletterFrequency: '',
    },
    // Advanced Form
    advancedForm: {
      productType: '',
      productName: '',
      price: '',
      weight: '',
      dimensions: '',
      fileType: '',
      downloadUrl: '',
      billingCycle: '',
      trialPeriod: '0',
      categories: [],
      productImage: null,
    },
  };

  // Form submission handlers
  const handleSubmitUserForm = (values, { setSubmitting, resetForm }) => {
    setTimeout(() => {
      setSubmitting(false);
      resetForm();
      showNotification('User registration completed successfully!');
    }, 800);
  };

  const handleSubmitConditionalForm = (values, { setSubmitting, resetForm }) => {
    setTimeout(() => {
      setSubmitting(false);
      resetForm();
      showNotification('Contact preferences saved successfully!');
    }, 800);
  };

  const handleSubmitAdvancedForm = (values, { setSubmitting, resetForm }) => {
    setTimeout(() => {
      setSubmitting(false);
      resetForm();
      showNotification('Product added successfully!');
    }, 800);
  };

  // Get the current form based on active tab
  const getCurrentForm = () => {
    switch (activeTab) {
      case 0:
        return (
          <FormBuilder
            fields={userFormFields}
            initialValues={formInitialValues.userForm}
            onSubmit={handleSubmitUserForm}
            title='User Registration Form'
            submitButtonText='Register User'
            formLayout={{
              spacing: 3,
              direction: 'row',
              containerProps: { sx: { mt: 2 } },
              submitButtonProps: { size: 'large' },
            }}
          />
        );
      case 1:
        return (
          <FormBuilder
            fields={conditionalFormFields}
            initialValues={formInitialValues.conditionalForm}
            onSubmit={handleSubmitConditionalForm}
            title='Contact Preferences Form'
            submitButtonText='Save Preferences'
            formLayout={{
              spacing: 3,
              direction: 'row',
              containerProps: { sx: { mt: 2 } },
            }}
          />
        );
      case 2:
        return (
          <FormBuilder
            fields={advancedFormFields}
            initialValues={formInitialValues.advancedForm}
            onSubmit={handleSubmitAdvancedForm}
            title='Product Creation Form'
            submitButtonText='Add Product'
            formLayout={{
              spacing: 3,
              direction: 'row',
              containerProps: { sx: { mt: 2 } },
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Container maxWidth='md' sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box mb={4}>
          <Typography variant='h4' component='h1' gutterBottom>
            Form Builder Examples
          </Typography>
          <Typography variant='body1' color='textSecondary' paragraph>
            These examples demonstrate how to use the FormBuilder component to create different
            types of forms. The FormBuilder supports conditional fields, custom validation, and
            various field types.
          </Typography>

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              aria-label='form examples tabs'
              variant='fullWidth'
            >
              <Tab label='Basic User Form' />
              <Tab label='Conditional Fields' />
              <Tab label='Advanced Product Form' />
            </Tabs>
          </Box>
        </Box>

        {getCurrentForm()}
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default FormBuilderExample;

import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  IconButton,
  Box,
  Tooltip,
} from '@mui/material';
import { Edit as EditIcon, Add as AddIcon } from '@mui/icons-material';
import VersionFormModal from './VersionFormModal';
import TextDisplayDialog from '../TextDisplayDialog/TextDisplayDialog';

/**
 * A reusable dialog component for managing versions of entities (prompts, personas, questionnaires)
 *
 * @param {Object} props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Array} props.versions - Array of version objects
 * @param {Object} props.entity - The parent entity (prompt, persona, questionnaire)
 * @param {Function} props.fetchVersions - Function to fetch versions
 * @param {Function} props.createVersion - Function to create a new version
 * @param {Function} props.updateVersion - Function to update a version
 * @param {Object} props.fields - Configuration for version fields
 * @param {string} props.entityType - Type of entity ('prompt', 'persona', 'questionnaire')
 */
const VersionsDialog = ({
  open,

  onClose,
  versions = [],

  entity,
  fetchVersions,
  createVersion,
  updateVersion,
  fields,
  entityType,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [versionFormOpen, setVersionFormOpen] = useState(false);
  const [versionFormMode, setVersionFormMode] = useState('create');
  const [selectedVersion, setSelectedVersion] = useState(null);

  useEffect(() => {
    if (open && entity?.id) {
      loadVersions();
    }
  }, [open, entity?.id]);

  const loadVersions = async () => {
    if (!entity?.id) return;

    setLoading(true);
    setError(null);
    try {
      await fetchVersions(entity.id);
    } catch (err) {
      console.error(`Error loading ${entityType} versions:`, err);
      setError(`Failed to load ${entityType} versions`);
    } finally {
      setLoading(false);
    }
  };

  const handleAddVersion = () => {
    setVersionFormMode('create');
    // Ensure all required fields are present and not null
    const baseFields = {
      prompt_text: '',
      model: 'gpt-4',
      status: 'Active',
      type: 'Conversational',
      change_notes: '',
    };
    // Use current_version as base if available, fallback to empty string
    const current = entity.current_version || {};
    const initialValues = {
      ...baseFields,
      ...current,
      change_notes: '',
      [`${entityType}_id`]: entity.id,
      persona_id: entity.persona_id || '',
    };
    setSelectedVersion(initialValues);
    setVersionFormOpen(true);
  };

  const handleEditVersion = (version) => {
    setVersionFormMode('edit');
    setSelectedVersion(version);
    setVersionFormOpen(true);
  };

  const handleVersionFormSubmit = async (values) => {
    setLoading(true);
    setError(null);
    try {
      if (versionFormMode === 'create') {
        await createVersion(entity.id, values);
      } else {
        await updateVersion(values.id, values);
      }
      setVersionFormOpen(false);
      loadVersions();
    } catch (err) {
      console.error(`Error saving ${entityType} version:`, err);
      setError(`Failed to save ${entityType} version`);
    } finally {
      setLoading(false);
    }
  };

  const getFieldValue = (version, field) => {
    if (!version) return '-';

    if (field === 'created_at') {
      return version.created_at ? new Date(version.created_at).toLocaleString() : '-';
    }

    if (field === 'status') {
      return version.status || '-';
    }

    return version[field] || '-';
  };

  const [textDialog, setTextDialog] = useState({
    open: false,
    title: '',
    content: '',
  });

  const handleOpenTextDialog = (content, title = 'Prompt Text') => {
    setTextDialog({
      open: true,
      title,
      content: content || '',
    });
  };

  const handleCloseTextDialog = () => {
    setTextDialog((prev) => ({
      ...prev,
      open: false,
    }));
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
        <DialogTitle>
          <Box display='flex' justifyContent='space-between' alignItems='center'>
            <Typography variant='h6'>
              {entityType.charAt(0).toUpperCase() + entityType.slice(1)} Versions
            </Typography>
            <Button
              variant='contained'
              color='primary'
              startIcon={<AddIcon />}
              onClick={handleAddVersion}
            >
              Add Version
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent>
          {error && <Typography color='error'>{error}</Typography>}

          {loading ? (
            <Typography>Loading versions...</Typography>
          ) : versions.length === 0 ? (
            <Typography>No versions found.</Typography>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Version</TableCell>
                    {fields.map((field) => (
                      <TableCell key={field.name}>{field.label}</TableCell>
                    ))}
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {versions.map((version) => (
                    <TableRow key={version.id}>
                      <TableCell>{version.version}</TableCell>
                      {fields.map((field) => {
                        const value = getFieldValue(version, field.name);
                        return (
                          <TableCell key={field.name}>
                            {field.name === 'change_notes' ? (
                              <Tooltip title={value}>
                                <span>
                                  {value.substring(0, 50)}
                                  {value.length > 50 ? '...' : ''}
                                </span>
                              </Tooltip>
                            ) : field.name === 'prompt_text' ? (
                              <Typography
                                sx={{
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  lineHeight: '1.2em',
                                  maxHeight: '2.4em',
                                  cursor: 'pointer',
                                  '&:hover': {
                                    textDecoration: 'underline',
                                    textDecorationColor: 'primary.main',
                                  },
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenTextDialog(value, field.name);

                                  // You can add a dialog here to show full text if needed
                                }}
                              >
                                {value}
                              </Typography>
                            ) : (
                              value
                            )}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        <IconButton onClick={() => handleEditVersion(version)} size='small'>
                          <EditIcon fontSize='small' />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>

      <VersionFormModal
        open={versionFormOpen}
        onClose={() => setVersionFormOpen(false)}
        onSubmit={handleVersionFormSubmit}
        initialValues={selectedVersion}
        mode={versionFormMode}
        fields={fields}
        entityType={entityType}
      />
      <TextDisplayDialog
        open={textDialog.open}
        onClose={handleCloseTextDialog}
        title={textDialog.title}
        content={textDialog.content}
        showLineNumbers={true}
      />
    </>
  );
};

export default VersionsDialog;

import React, { useEffect, useRef, useState } from 'react';
import { Box, useTheme } from '@mui/material';
import { styled } from '@mui/material/styles';

const WaveformContainer = styled(Box)(({ theme }) => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: 'linear-gradient(135deg, rgba(94, 74, 227, 0.05) 0%, rgba(62, 219, 240, 0.05) 100%)',
  borderRadius: 16,
  padding: theme.spacing(2),
  position: 'relative',
  overflow: 'hidden',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(45deg, transparent 30%, rgba(62, 219, 240, 0.1) 50%, transparent 70%)',
    animation: 'shimmer 3s infinite',
  },
  
  '@keyframes shimmer': {
    '0%': {
      transform: 'translateX(-100%)',
    },
    '100%': {
      transform: 'translateX(100%)',
    },
  },
}));

const WaveformCanvas = styled('canvas')(({ theme }) => ({
  width: '100%',
  height: '100%',
  borderRadius: 8,
  position: 'relative',
  zIndex: 1,
}));

/**
 * Real-time Waveform Visualizer Component
 * Displays animated audio waveforms with AI-powered styling
 */
const WaveformVisualizer = ({
  isActive = false,
  intensity = 0.5,
  barCount = 32,
  height = 120,
  color = 'primary',
  animated = true,
  style = 'bars', // 'bars', 'wave', 'circular'
  ...props
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [audioData, setAudioData] = useState(new Array(barCount).fill(0));
  const theme = useTheme();

  // Generate random audio data for demo purposes
  const generateAudioData = () => {
    if (!isActive) {
      return new Array(barCount).fill(0);
    }
    
    return Array.from({ length: barCount }, (_, i) => {
      const baseHeight = Math.sin((Date.now() * 0.001) + (i * 0.2)) * 0.5 + 0.5;
      const randomVariation = Math.random() * 0.3;
      return Math.min(1, (baseHeight + randomVariation) * intensity);
    });
  };

  // Get color based on theme and prop
  const getColor = () => {
    switch (color) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      default:
        return color;
    }
  };

  // Draw bars style waveform
  const drawBars = (ctx, canvas, data) => {
    const barWidth = canvas.width / barCount;
    const maxBarHeight = canvas.height * 0.8;
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    data.forEach((value, index) => {
      const barHeight = value * maxBarHeight;
      const x = index * barWidth;
      const y = (canvas.height - barHeight) / 2;
      
      // Create gradient for each bar
      const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
      gradient.addColorStop(0, getColor());
      gradient.addColorStop(0.5, theme.palette.secondary.main);
      gradient.addColorStop(1, getColor());
      
      ctx.fillStyle = gradient;
      ctx.fillRect(x + 2, y, barWidth - 4, barHeight);
      
      // Add glow effect
      if (isActive && value > 0.3) {
        ctx.shadowColor = getColor();
        ctx.shadowBlur = 10;
        ctx.fillRect(x + 2, y, barWidth - 4, barHeight);
        ctx.shadowBlur = 0;
      }
    });
  };

  // Draw wave style waveform
  const drawWave = (ctx, canvas, data) => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const centerY = canvas.height / 2;
    const amplitude = canvas.height * 0.4;
    
    // Create gradient
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
    gradient.addColorStop(0, getColor());
    gradient.addColorStop(0.5, theme.palette.secondary.main);
    gradient.addColorStop(1, getColor());
    
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 3;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    if (isActive) {
      ctx.shadowColor = getColor();
      ctx.shadowBlur = 8;
    }
    
    ctx.beginPath();
    
    data.forEach((value, index) => {
      const x = (index / (data.length - 1)) * canvas.width;
      const y = centerY + (value - 0.5) * amplitude;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    ctx.shadowBlur = 0;
  };

  // Draw circular style waveform
  const drawCircular = (ctx, canvas, data) => {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const baseRadius = Math.min(canvas.width, canvas.height) * 0.2;
    const maxRadius = Math.min(canvas.width, canvas.height) * 0.4;
    
    const gradient = ctx.createRadialGradient(centerX, centerY, baseRadius, centerX, centerY, maxRadius);
    gradient.addColorStop(0, getColor());
    gradient.addColorStop(1, theme.palette.secondary.main);
    
    ctx.strokeStyle = gradient;
    ctx.lineWidth = 2;
    
    if (isActive) {
      ctx.shadowColor = getColor();
      ctx.shadowBlur = 10;
    }
    
    data.forEach((value, index) => {
      const angle = (index / data.length) * Math.PI * 2;
      const radius = baseRadius + (value * (maxRadius - baseRadius));
      
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      ctx.beginPath();
      ctx.arc(x, y, 2, 0, Math.PI * 2);
      ctx.fill();
    });
    
    ctx.shadowBlur = 0;
  };

  // Animation loop
  const animate = () => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Update audio data
    const newData = generateAudioData();
    setAudioData(newData);
    
    // Draw based on style
    switch (style) {
      case 'wave':
        drawWave(ctx, canvas, newData);
        break;
      case 'circular':
        drawCircular(ctx, canvas, newData);
        break;
      default:
        drawBars(ctx, canvas, newData);
    }
    
    if (animated) {
      animationRef.current = requestAnimationFrame(animate);
    }
  };

  // Setup canvas and start animation
  useEffect(() => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = canvas.offsetWidth * window.devicePixelRatio;
    canvas.height = canvas.offsetHeight * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    
    if (animated) {
      animate();
    } else {
      // Draw static waveform
      const staticData = generateAudioData();
      switch (style) {
        case 'wave':
          drawWave(ctx, canvas, staticData);
          break;
        case 'circular':
          drawCircular(ctx, canvas, staticData);
          break;
        default:
          drawBars(ctx, canvas, staticData);
      }
    }
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, intensity, barCount, animated, style, color]);

  return (
    <WaveformContainer sx={{ height }} {...props}>
      <WaveformCanvas ref={canvasRef} />
    </WaveformContainer>
  );
};

export default WaveformVisualizer;

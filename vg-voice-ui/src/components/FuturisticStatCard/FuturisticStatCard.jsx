import React from 'react';
import { 
  Card, 
  CardContent, 
  Typography, 
  Box, 
  Avatar, 
  useTheme,
  LinearProgress,
  Chip
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import { TrendingUp, TrendingDown } from '@mui/icons-material';

// Floating animation
const float = keyframes`
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
`;

// Glow animation
const glow = keyframes`
  0% {
    box-shadow: 0 8px 32px rgba(94, 74, 227, 0.1);
  }
  50% {
    box-shadow: 0 16px 48px rgba(94, 74, 227, 0.3);
  }
  100% {
    box-shadow: 0 8px 32px rgba(94, 74, 227, 0.1);
  }
`;

// Styled Card with gradient background
const StyledCard = styled(Card)(({ theme, variant, glowing }) => ({
  position: 'relative',
  overflow: 'hidden',
  borderRadius: 20,
  border: '1px solid rgba(94, 74, 227, 0.2)',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  
  background: getCardBackground(variant, theme),
  
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    animation: glowing ? `${glow} 2s infinite` : 'none',
  },
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    transition: 'left 0.6s',
    zIndex: 1,
  },
  
  '&:hover::before': {
    left: '100%',
  },
  
  '& .MuiCardContent-root': {
    position: 'relative',
    zIndex: 2,
  },
}));

// Animated progress bar
const AnimatedProgress = styled(LinearProgress)(({ theme, color }) => ({
  height: 6,
  borderRadius: 3,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  
  '& .MuiLinearProgress-bar': {
    borderRadius: 3,
    background: getProgressGradient(color, theme),
    transition: 'transform 1s ease-in-out',
  },
}));

// Icon container with floating animation
const IconContainer = styled(Avatar)(({ theme, variant, animated }) => ({
  width: 56,
  height: 56,
  background: getIconBackground(variant, theme),
  animation: animated ? `${float} 3s ease-in-out infinite` : 'none',
  
  '& .MuiSvgIcon-root': {
    fontSize: '1.8rem',
    color: getIconColor(variant, theme),
  },
}));

// Trend indicator
const TrendIndicator = styled(Box)(({ theme, trend }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
  padding: theme.spacing(0.5, 1),
  borderRadius: 12,
  backgroundColor: trend === 'up' ? 'rgba(152, 245, 225, 0.2)' : 'rgba(255, 183, 77, 0.2)',
  color: trend === 'up' ? theme.palette.success.main : theme.palette.warning.main,
  
  '& .MuiSvgIcon-root': {
    fontSize: '1rem',
  },
}));

// Helper functions
function getCardBackground(variant, theme) {
  switch (variant) {
    case 'primary':
      return 'linear-gradient(135deg, rgba(94, 74, 227, 0.1) 0%, rgba(62, 219, 240, 0.1) 100%)';
    case 'success':
      return 'linear-gradient(135deg, rgba(152, 245, 225, 0.1) 0%, rgba(102, 187, 106, 0.1) 100%)';
    case 'warning':
      return 'linear-gradient(135deg, rgba(255, 183, 77, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%)';
    case 'error':
      return 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 183, 77, 0.1) 100%)';
    case 'gradient':
      return 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)';
    default:
      return theme.palette.background.paper;
  }
}

function getIconBackground(variant, theme) {
  switch (variant) {
    case 'primary':
      return 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)';
    case 'success':
      return 'linear-gradient(135deg, #98F5E1 0%, #66BB6A 100%)';
    case 'warning':
      return 'linear-gradient(135deg, #FFB74D 0%, #FF9800 100%)';
    case 'error':
      return 'linear-gradient(135deg, #f44336 0%, #FFB74D 100%)';
    case 'gradient':
      return 'rgba(255, 255, 255, 0.2)';
    default:
      return theme.palette.grey[200];
  }
}

function getIconColor(variant, theme) {
  switch (variant) {
    case 'gradient':
      return '#FFFFFF';
    default:
      return '#FFFFFF';
  }
}

function getProgressGradient(color, theme) {
  switch (color) {
    case 'primary':
      return 'linear-gradient(90deg, #5E4AE3 0%, #3EDBF0 100%)';
    case 'success':
      return 'linear-gradient(90deg, #98F5E1 0%, #66BB6A 100%)';
    case 'warning':
      return 'linear-gradient(90deg, #FFB74D 0%, #FF9800 100%)';
    case 'error':
      return 'linear-gradient(90deg, #f44336 0%, #FFB74D 100%)';
    default:
      return theme.palette.primary.main;
  }
}

function getTextColor(variant) {
  return variant === 'gradient' ? '#FFFFFF' : 'inherit';
}

/**
 * Futuristic Stat Card Component
 * Enhanced stat card with gradients, animations, and AI-powered styling
 */
const FuturisticStatCard = ({
  title,
  value,
  subtitle,
  icon,
  variant = 'default',
  trend,
  trendValue,
  progress,
  progressColor = 'primary',
  animated = true,
  glowing = false,
  onClick,
  chip,
  ...props
}) => {
  const theme = useTheme();
  const textColor = getTextColor(variant);

  return (
    <StyledCard 
      variant={variant} 
      glowing={glowing}
      onClick={onClick}
      {...props}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                color: variant === 'gradient' ? 'rgba(255,255,255,0.8)' : 'text.secondary',
                fontWeight: 500,
                mb: 1
              }}
            >
              {title}
            </Typography>
            
            <Typography 
              variant="h4" 
              sx={{ 
                fontWeight: 700,
                color: textColor,
                mb: 1,
                background: variant === 'gradient' ? 'none' : 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
                WebkitBackgroundClip: variant === 'gradient' ? 'none' : 'text',
                WebkitTextFillColor: variant === 'gradient' ? 'inherit' : 'transparent',
                backgroundClip: variant === 'gradient' ? 'none' : 'text',
              }}
            >
              {value}
            </Typography>
            
            {subtitle && (
              <Typography 
                variant="body2" 
                sx={{ 
                  color: variant === 'gradient' ? 'rgba(255,255,255,0.7)' : 'text.secondary',
                  fontWeight: 400
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
          
          {icon && (
            <IconContainer variant={variant} animated={animated}>
              {icon}
            </IconContainer>
          )}
        </Box>
        
        {/* Trend Indicator */}
        {trend && trendValue && (
          <Box sx={{ mb: 2 }}>
            <TrendIndicator trend={trend}>
              {trend === 'up' ? <TrendingUp /> : <TrendingDown />}
              <Typography variant="caption" sx={{ fontWeight: 600 }}>
                {trendValue}
              </Typography>
            </TrendIndicator>
          </Box>
        )}
        
        {/* Progress Bar */}
        {progress !== undefined && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption" sx={{ color: textColor }}>
                Progress
              </Typography>
              <Typography variant="caption" sx={{ color: textColor, fontWeight: 600 }}>
                {Math.round(progress)}%
              </Typography>
            </Box>
            <AnimatedProgress 
              variant="determinate" 
              value={progress} 
              color={progressColor}
            />
          </Box>
        )}
        
        {/* Chip */}
        {chip && (
          <Box sx={{ mt: 2 }}>
            <Chip 
              {...chip}
              size="small"
              sx={{
                borderRadius: 2,
                fontWeight: 500,
                ...chip.sx
              }}
            />
          </Box>
        )}
      </CardContent>
    </StyledCard>
  );
};

export default FuturisticStatCard;

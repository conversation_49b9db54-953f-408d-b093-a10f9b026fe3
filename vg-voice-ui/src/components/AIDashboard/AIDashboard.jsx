import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  IconButton,
  useTheme,
  Container,
  Fade,
  Slide,
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import {
  Phone,
  SmartToy,
  Speed,
  AccessTime,
  Refresh,
  PlayArrow,
  VolumeUp,
  Analytics,
} from '@mui/icons-material';

// Import our custom components
import FuturisticStatCard from '../FuturisticStatCard';
import WaveformVisualizer from '../WaveformVisualizer';
import AIStatusIndicator from '../AIStatusIndicator';

// Background animation
const backgroundShift = keyframes`
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
`;

// Styled components
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `
    linear-gradient(135deg, 
      rgba(94, 74, 227, 0.03) 0%, 
      rgba(62, 219, 240, 0.03) 25%,
      rgba(152, 245, 225, 0.03) 50%,
      rgba(94, 74, 227, 0.03) 75%,
      rgba(62, 219, 240, 0.03) 100%
    )
  `,
  backgroundSize: '400% 400%',
  animation: `${backgroundShift} 20s ease infinite`,
  position: 'relative',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 80%, rgba(94, 74, 227, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(62, 219, 240, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(152, 245, 225, 0.05) 0%, transparent 50%)
    `,
    pointerEvents: 'none',
  },
}));

const HeaderSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(4, 0),
  textAlign: 'center',
  position: 'relative',
  zIndex: 1,
}));

const MainContent = styled(Container)(({ theme }) => ({
  position: 'relative',
  zIndex: 1,
  paddingBottom: theme.spacing(6),
}));

const ControlPanel = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: 20,
  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(94, 74, 227, 0.1)',
  boxShadow: '0 16px 48px rgba(94, 74, 227, 0.1)',
  marginBottom: theme.spacing(4),
}));

const LiveCallSection = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: 20,
  background: 'linear-gradient(135deg, rgba(94, 74, 227, 0.05) 0%, rgba(62, 219, 240, 0.05) 100%)',
  border: '1px solid rgba(94, 74, 227, 0.2)',
  boxShadow: '0 16px 48px rgba(94, 74, 227, 0.15)',
  position: 'relative',
  overflow: 'hidden',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #5E4AE3 0%, #3EDBF0 100%)',
  },
}));

/**
 * AI-Powered Calling Dashboard Component
 * Modern, futuristic dashboard with real-time visualizations
 */
const AIDashboard = () => {
  const theme = useTheme();
  const [isCallActive, setIsCallActive] = useState(false);
  const [callStatus, setCallStatus] = useState('idle');
  const [stats, setStats] = useState({
    totalCalls: 1247,
    activeConnections: 8,
    successRate: 94.2,
    avgDuration: 185,
  });

  // Simulate call status changes
  useEffect(() => {
    if (isCallActive) {
      const statusSequence = ['calling', 'connected', 'ai_processing', 'speaking', 'completed'];
      let currentIndex = 0;
      
      const interval = setInterval(() => {
        setCallStatus(statusSequence[currentIndex]);
        currentIndex++;
        
        if (currentIndex >= statusSequence.length) {
          setIsCallActive(false);
          setCallStatus('idle');
          clearInterval(interval);
        }
      }, 3000);
      
      return () => clearInterval(interval);
    }
  }, [isCallActive]);

  const handleStartCall = () => {
    setIsCallActive(true);
    setCallStatus('calling');
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  return (
    <DashboardContainer>
      <HeaderSection>
        <Fade in timeout={1000}>
          <Box>
            <Typography 
              variant="h3" 
              sx={{ 
                fontWeight: 700,
                background: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                mb: 2,
              }}
            >
              AI Voice Command Center
            </Typography>
            <Typography 
              variant="h6" 
              sx={{ 
                color: 'text.secondary',
                fontWeight: 400,
                maxWidth: 600,
                mx: 'auto',
              }}
            >
              Intelligent calling dashboard with real-time AI processing and analytics
            </Typography>
          </Box>
        </Fade>
      </HeaderSection>

      <MainContent maxWidth="xl">
        {/* Control Panel */}
        <Slide in direction="up" timeout={800}>
          <ControlPanel>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Call Control Center
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <IconButton 
                  color="primary"
                  sx={{ 
                    background: 'linear-gradient(135deg, rgba(94, 74, 227, 0.1) 0%, rgba(62, 219, 240, 0.1) 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, rgba(94, 74, 227, 0.2) 0%, rgba(62, 219, 240, 0.2) 100%)',
                    }
                  }}
                >
                  <Refresh />
                </IconButton>
                <Button
                  variant="contained"
                  startIcon={<PlayArrow />}
                  onClick={handleStartCall}
                  disabled={isCallActive}
                  sx={{
                    borderRadius: 3,
                    px: 4,
                    py: 1.5,
                    background: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #4B0082 0%, #00BFFF 100%)',
                    },
                    '&:disabled': {
                      background: 'linear-gradient(135deg, rgba(94, 74, 227, 0.5) 0%, rgba(62, 219, 240, 0.5) 100%)',
                    }
                  }}
                >
                  {isCallActive ? 'Call in Progress' : 'Start AI Call'}
                </Button>
              </Box>
            </Box>

            <AIStatusIndicator 
              status={callStatus} 
              animated={isCallActive}
              showChip={false}
              sx={{ mb: 2 }}
            />
          </ControlPanel>
        </Slide>

        {/* Stats Grid */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Slide in direction="up" timeout={1000}>
              <div>
                <FuturisticStatCard
                  title="Total Calls Today"
                  value={stats.totalCalls.toLocaleString()}
                  subtitle="Across all campaigns"
                  icon={<Phone />}
                  variant="primary"
                  trend="up"
                  trendValue="+12.5%"
                  animated
                />
              </div>
            </Slide>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Slide in direction="up" timeout={1200}>
              <div>
                <FuturisticStatCard
                  title="Active Connections"
                  value={stats.activeConnections}
                  subtitle="Live AI conversations"
                  icon={<SmartToy />}
                  variant="success"
                  progress={75}
                  progressColor="success"
                  animated
                  glowing={isCallActive}
                />
              </div>
            </Slide>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Slide in direction="up" timeout={1400}>
              <div>
                <FuturisticStatCard
                  title="Success Rate"
                  value={`${stats.successRate}%`}
                  subtitle="AI conversation completion"
                  icon={<Speed />}
                  variant="gradient"
                  trend="up"
                  trendValue="+3.2%"
                  animated
                />
              </div>
            </Slide>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Slide in direction="up" timeout={1600}>
              <div>
                <FuturisticStatCard
                  title="Avg Duration"
                  value={formatDuration(stats.avgDuration)}
                  subtitle="Per conversation"
                  icon={<AccessTime />}
                  variant="warning"
                  chip={{
                    label: "Optimized",
                    color: "success",
                    variant: "outlined"
                  }}
                  animated
                />
              </div>
            </Slide>
          </Grid>
        </Grid>

        {/* Live Call Visualization */}
        <Slide in direction="up" timeout={1800}>
          <LiveCallSection>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <VolumeUp sx={{ color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Real-time Audio Analysis
                </Typography>
              </Box>
              <AIStatusIndicator 
                status={isCallActive ? callStatus : 'idle'} 
                animated={isCallActive}
                size="small"
              />
            </Box>
            
            <WaveformVisualizer
              isActive={isCallActive}
              intensity={0.8}
              barCount={48}
              height={120}
              color="primary"
              style="bars"
              animated
            />
          </LiveCallSection>
        </Slide>
      </MainContent>
    </DashboardContainer>
  );
};

export default AIDashboard;

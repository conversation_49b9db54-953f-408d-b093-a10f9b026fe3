import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Box,
  Typography,
  CircularProgress,
  TextField,
  InputAdornment,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

/**
 * A reusable data table component built with Material UI
 * Supports pagination, sorting, searching, and customizable columns
 * Can handle both client-side pagination and server-side pagination
 *
 * @component
 */
const DataTable = ({
  columns,
  data,
  totalCount,
  title,
  loading = false,
  serverSidePagination = false,
  onPageChange,
  onRowsPerPageChange,
  onSort,
  initialSort = { field: '', direction: 'asc' },
  searchEnabled = true,
  onSearch,
  emptyStateMessage = 'No data available',
  customRowsPerPageOptions = [5, 10, 25],
  initialRowsPerPage = 10,
  initialPage = 0,
  onRowClick,
}) => {
  // State for pagination
  const [page, setPage] = useState(initialPage);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);

  // State for sorting
  const [sortConfig, setSortConfig] = useState(initialSort);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // State for data to display (when using client-side pagination)
  const [displayData, setDisplayData] = useState([]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    if (serverSidePagination && onPageChange) {
      onPageChange(newPage, rowsPerPage);
    }
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0); // Reset to first page when changing rows per page
    if (serverSidePagination && onRowsPerPageChange) {
      onRowsPerPageChange(newRowsPerPage);
    }
  };

  // Handle sorting
  const handleSort = (field) => {
    const isAsc = sortConfig.field === field && sortConfig.direction === 'asc';
    const newDirection = isAsc ? 'desc' : 'asc';
    const newSortConfig = { field, direction: newDirection };

    setSortConfig(newSortConfig);

    if (serverSidePagination && onSort) {
      onSort(field, newDirection);
    }
  };

  // Handle search query change
  const handleSearchChange = (event) => {
    const query = event.target.value;
    setSearchQuery(query);

    if (serverSidePagination && onSearch) {
      onSearch(query);
    }
  };

  // Process data for client-side pagination, sorting, and filtering
  useEffect(() => {
    if (!serverSidePagination && data) {
      let processedData = [...data];

      // Apply search filter if search is enabled
      if (searchEnabled && searchQuery) {
        processedData = processedData.filter((row) => {
          return Object.keys(row).some((key) => {
            // Skip non-string and non-number values
            if (typeof row[key] !== 'string' && typeof row[key] !== 'number') {
              return false;
            }
            return String(row[key]).toLowerCase().includes(searchQuery.toLowerCase());
          });
        });
      }

      // Apply sorting
      if (sortConfig.field) {
        processedData.sort((a, b) => {
          const aValue = a[sortConfig.field];
          const bValue = b[sortConfig.field];

          if (aValue === bValue) return 0;

          const comparison =
            sortConfig.direction === 'asc' ? (aValue < bValue ? -1 : 1) : aValue < bValue ? 1 : -1;

          return comparison;
        });
      }

      setDisplayData(processedData);
    }
  }, [data, sortConfig, searchQuery, serverSidePagination, searchEnabled]);

  // Calculate pagination values for client-side pagination
  const paginatedData =
    !serverSidePagination && displayData
      ? displayData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
      : data || [];

  // Calculate total number of items
  const totalItems = serverSidePagination ? totalCount : displayData?.length || 0;

  return (
    <Paper elevation={2} sx={{ width: '100%', overflow: 'hidden' }}>
      {/* Header with title and search */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        {title && (
          <Typography variant='h6' component='div'>
            {title}
          </Typography>
        )}

        {searchEnabled && (
          <TextField
            variant='outlined'
            placeholder='Search...'
            size='small'
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 250 }}
          />
        )}
      </Box>

      {/* Table */}
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label='data table'>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  align={column.numeric ? 'right' : 'left'}
                  sx={{ fontWeight: 'bold', minWidth: column.minWidth }}
                  sortDirection={sortConfig.field === column.field ? sortConfig.direction : false}
                >
                  {column.sortable !== false ? (
                    <TableSortLabel
                      active={sortConfig.field === column.field}
                      direction={sortConfig.field === column.field ? sortConfig.direction : 'asc'}
                      onClick={() => handleSort(column.field)}
                    >
                      {column.headerName || column.field}
                    </TableSortLabel>
                  ) : (
                    column.headerName || column.field
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} align='center' sx={{ py: 3 }}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : paginatedData.length > 0 ? (
              paginatedData.map((row, rowIndex) => (
                <TableRow
                  hover
                  role='checkbox'
                  tabIndex={-1}
                  key={row.id || rowIndex}
                  sx={{
                    '&:last-child td, &:last-child th': { border: 0 },
                    cursor: onRowClick ? 'pointer' : 'default',
                  }}
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                >
                  {columns.map((column) => {
                    const value = column.valueGetter
                      ? column.valueGetter({ row, field: column.field })
                      : row[column.field];
                    return (
                      <TableCell key={column.field} align={column.numeric ? 'right' : 'left'}>
                        {column.renderCell
                          ? column.renderCell({ value, row, field: column.field })
                          : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} align='center' sx={{ py: 3 }}>
                  {emptyStateMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={customRowsPerPageOptions}
        component='div'
        count={totalItems}
        rowsPerPage={rowsPerPage}
        page={totalItems <= 0 ? 0 : page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

DataTable.propTypes = {
  /** Array of column configuration objects */
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      /** Unique field identifier that matches keys in data objects */
      field: PropTypes.string.isRequired,
      /** Display name for the column header */
      headerName: PropTypes.string,
      /** Minimum width of the column */
      minWidth: PropTypes.number,
      /** Whether the column contains numeric data (affects alignment) */
      numeric: PropTypes.bool,
      /** Function to render custom cell content */
      renderCell: PropTypes.func,
      /** Whether the column is sortable */
      sortable: PropTypes.bool,
    })
  ).isRequired,
  /** Array of data objects to display in the table */
  data: PropTypes.array.isRequired,
  /** Total count of items (for server-side pagination) */
  totalCount: PropTypes.number,
  /** Table title displayed in the header */
  title: PropTypes.string,
  /** Whether the table is in loading state */
  loading: PropTypes.bool,
  /** Whether to use server-side pagination */
  serverSidePagination: PropTypes.bool,
  /** Callback for page change - function(page, rowsPerPage) */
  onPageChange: PropTypes.func,
  /** Callback for rows per page change - function(rowsPerPage) */
  onRowsPerPageChange: PropTypes.func,
  /** Callback for sort change - function(field, direction) */
  onSort: PropTypes.func,
  /** Initial sort configuration */
  initialSort: PropTypes.shape({
    field: PropTypes.string,
    direction: PropTypes.oneOf(['asc', 'desc']),
  }),
  /** Whether search functionality is enabled */
  searchEnabled: PropTypes.bool,
  /** Callback for search query change - function(query) */
  onSearch: PropTypes.func,
  /** Message to display when there is no data */
  emptyStateMessage: PropTypes.string,
  /** Options for rows per page selector */
  customRowsPerPageOptions: PropTypes.arrayOf(PropTypes.number),
  /** Initial rows per page value */
  initialRowsPerPage: PropTypes.number,
  /** Initial page index */
  initialPage: PropTypes.number,
  /** Callback function when a row is clicked - function(row) */
  onRowClick: PropTypes.func,
};

export default DataTable;

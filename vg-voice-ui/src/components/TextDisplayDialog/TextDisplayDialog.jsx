import React from 'react';
import { Dialog, DialogTitle, DialogContent, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const TextDisplayDialog = ({
  open,
  onClose,
  title = 'Text Content',
  content = '',
  maxWidth = 'md',
  showLineNumbers = false,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
          maxHeight: '80vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: 'primary.main',
          color: 'primary.contrastText',
          py: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography variant='h6'>{title}</Typography>
        <IconButton edge='end' color='inherit' onClick={onClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 3 }}>
        <Typography
          component='div'
          sx={{
            whiteSpace: 'pre-wrap',
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            lineHeight: 1.5,
            '& .line': {
              display: 'block',
              minHeight: '1.5em',
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            },
            '& .lineNumber': {
              display: 'inline-block',
              width: '2em',
              color: 'text.secondary',
              userSelect: 'none',
              marginRight: 2,
              textAlign: 'right',
            },
          }}
        >
          {showLineNumbers
            ? content.split('\n').map((line, i) => (
                <span key={i} className='line'>
                  <span className='lineNumber'>{i + 1}</span>
                  {line || ' '}
                </span>
              ))
            : content}
        </Typography>
      </DialogContent>
    </Dialog>
  );
};

export default TextDisplayDialog;

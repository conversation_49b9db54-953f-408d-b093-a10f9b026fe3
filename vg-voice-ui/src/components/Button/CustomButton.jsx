import React from 'react';
import PropTypes from 'prop-types';
import { Button, CircularProgress, styled, alpha } from '@mui/material';

// Styled button component with hover effects based on variant
const StyledButton = styled(Button)(({ theme, customcolor, variant }) => ({
  borderRadius: variant === 'rounded' ? '20px' : '4px',
  textTransform: 'none',
  fontWeight: 600,
  position: 'relative',
  boxShadow: variant === 'elevated' ? theme.shadows[2] : 'none',
  transition: 'all 0.3s ease',

  // Apply custom color if provided
  ...(customcolor &&
    variant === 'contained' && {
      backgroundColor: customcolor,
      color: theme.palette.getContrastText(customcolor),
      '&:hover': {
        backgroundColor: alpha(customcolor, 0.9),
      },
    }),

  ...(customcolor &&
    variant === 'outlined' && {
      borderColor: customcolor,
      color: customcolor,
      '&:hover': {
        borderColor: customcolor,
        backgroundColor: alpha(customcolor, 0.1),
      },
    }),

  // Add hover effects
  '&:hover': {
    transform: variant === 'elevated' ? 'translateY(-2px)' : 'none',
    boxShadow: variant === 'elevated' ? theme.shadows[4] : 'none',
  },

  // Adjust padding based on size
  ...(variant === 'icon' && {
    minWidth: 'auto',
    padding: theme.spacing(1),
    borderRadius: '50%',
  }),
}));

/**
 * A reusable button component with various styles and states
 *
 * @component
 */
const CustomButton = ({
  children,
  variant = 'contained',
  color = 'primary',
  customColor,
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  startIcon,
  endIcon,
  onClick,
  type = 'button',
  buttonStyle = 'default',
  className,
  sx,
  ...props
}) => {
  // Determine MUI variant based on our buttonStyle prop
  const getMuiVariant = () => {
    switch (buttonStyle) {
      case 'outlined':
        return 'outlined';
      case 'text':
        return 'text';
      case 'icon':
        return 'contained';
      default:
        return 'contained';
    }
  };

  // Add loading spinner if loading prop is true
  const renderChildren = () => {
    if (loading) {
      return (
        <>
          <CircularProgress
            size={24}
            color='inherit'
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              marginTop: '-12px',
              marginLeft: '-12px',
            }}
          />
          <span style={{ visibility: 'hidden' }}>{children}</span>
        </>
      );
    }
    return children;
  };

  return (
    <StyledButton
      variant={getMuiVariant()}
      color={color}
      customcolor={customColor}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      onClick={!loading && !disabled ? onClick : undefined}
      type={type}
      startIcon={!loading ? startIcon : null}
      endIcon={!loading ? endIcon : null}
      className={className}
      sx={sx}
      {...props}
    >
      {renderChildren()}
    </StyledButton>
  );
};

CustomButton.propTypes = {
  /** Button content */
  children: PropTypes.node.isRequired,
  /** Material UI color variants */
  color: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'error',
    'info',
    'warning',
    'inherit',
  ]),
  /** Custom color (hex, rgb, etc.) - overrides color prop */
  customColor: PropTypes.string,
  /** Button size */
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  /** Whether the button should take full width of its container */
  fullWidth: PropTypes.bool,
  /** Whether the button is disabled */
  disabled: PropTypes.bool,
  /** Whether to show a loading spinner */
  loading: PropTypes.bool,
  /** Icon to show at the start of the button */
  startIcon: PropTypes.node,
  /** Icon to show at the end of the button */
  endIcon: PropTypes.node,
  /** Click handler function */
  onClick: PropTypes.func,
  /** Button type attribute */
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  /** Button visual style */
  buttonStyle: PropTypes.oneOf(['default', 'outlined', 'text', 'elevated', 'rounded', 'icon']),
  /** Additional CSS class */
  className: PropTypes.string,
  /** Material UI sx prop for custom styles */
  sx: PropTypes.object,
};

export default CustomButton;

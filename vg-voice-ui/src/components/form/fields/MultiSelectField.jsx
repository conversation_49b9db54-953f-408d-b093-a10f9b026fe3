import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  Box,
  OutlinedInput,
} from '@mui/material';
import { useField } from 'formik';

/**
 * Multi-select dropdown component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const MultiSelectField = ({ label, options, helperText, required, chipProps = {}, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const isError = Boolean(meta.touched && meta.error);

  // Handle selection changes
  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    helpers.setValue(value);
  };

  return (
    <FormControl fullWidth variant='outlined' error={isError} margin='normal'>
      <InputLabel id={`${props.id || props.name}-label`}>
        {required ? `${label} *` : label}
      </InputLabel>
      <Select
        {...field}
        {...props}
        multiple
        labelId={`${props.id || props.name}-label`}
        input={
          <OutlinedInput label={required ? `${label} *` : label} id={props.id || props.name} />
        }
        value={field.value || []}
        onChange={handleChange}
        renderValue={(selected) => (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {selected.map((value) => {
              const selectedOption = options.find((option) => option.value === value);
              return (
                <Chip
                  key={value}
                  label={selectedOption ? selectedOption.label : value}
                  {...chipProps}
                />
              );
            })}
          </Box>
        )}
      >
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {(isError || helperText) && (
        <FormHelperText>{isError ? meta.error : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default MultiSelectField;

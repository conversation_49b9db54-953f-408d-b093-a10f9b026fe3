import React from 'react';
import { FormControlLabel, Checkbox, FormHelperText, FormControl, FormGroup } from '@mui/material';
import { useField } from 'formik';

/**
 * Checkbox component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const CheckboxField = ({ label, helperText, ...props }) => {
  const [field, meta] = useField({ ...props, type: 'checkbox' });
  const isError = Boolean(meta.touched && meta.error);

  return (
    <FormControl error={isError} component='fieldset' margin='normal'>
      <FormGroup>
        <FormControlLabel
          control={<Checkbox {...field} {...props} checked={field.value} />}
          label={label}
        />
      </FormGroup>
      {(isError || helperText) && (
        <FormHelperText>{isError ? meta.error : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default CheckboxField;

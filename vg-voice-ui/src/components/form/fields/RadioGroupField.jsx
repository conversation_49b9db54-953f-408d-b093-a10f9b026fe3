import React from 'react';
import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText,
} from '@mui/material';
import { useField } from 'formik';

/**
 * Radio group component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const RadioGroupField = ({ label, options, helperText, required, row = false, ...props }) => {
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);

  return (
    <FormControl component='fieldset' error={isError} margin='normal' fullWidth>
      <FormLabel component='legend'>{required ? `${label} *` : label}</FormLabel>
      <RadioGroup {...field} {...props} row={row}>
        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={<Radio />}
            label={option.label}
          />
        ))}
      </RadioGroup>
      {(isError || helperText) && (
        <FormHelperText>{isError ? meta.error : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default RadioGroupField;

import * as Yup from 'yup';

/**
 * Common validation rules that can be reused across forms
 */
const validationRules = {
  // String validations
  required: Yup.string().required('This field is required'),
  email: Yup.string().email('Invalid email address').required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/[0-9]/, 'Password must contain at least one number')
    .matches(/[^a-zA-Z0-9]/, 'Password must contain at least one special character')
    .required('Password is required'),
  confirmPassword: (fieldName = 'password') =>
    Yup.string()
      .oneOf([Yup.ref(fieldName), null], 'Passwords must match')
      .required('Confirm password is required'),
  name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name must be less than 50 characters')
    .required('Name is required'),
  phone: Yup.string()
    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
    .required('Phone number is required'),

  // Number validations
  number: Yup.number().typeError('Must be a number').required('This field is required'),
  positiveNumber: Yup.number()
    .typeError('Must be a number')
    .positive('Must be a positive number')
    .required('This field is required'),
  integer: Yup.number()
    .typeError('Must be a number')
    .integer('Must be an integer')
    .required('This field is required'),

  // Date validations
  date: Yup.date().typeError('Must be a valid date').required('Date is required'),
  futureDate: Yup.date()
    .typeError('Must be a valid date')
    .min(new Date(), 'Date must be in the future')
    .required('Date is required'),
  pastDate: Yup.date()
    .typeError('Must be a valid date')
    .max(new Date(), 'Date must be in the past')
    .required('Date is required'),

  // Array validations
  array: Yup.array().required('This field is required'),
  nonEmptyArray: Yup.array()
    .min(1, 'At least one item is required')
    .required('This field is required'),

  // Boolean validations
  boolean: Yup.boolean().required('This field is required'),
  accepted: Yup.boolean()
    .oneOf([true], 'You must accept the terms')
    .required('This field is required'),

  // Custom validation factory
  customValidation: (validationFn, errorMessage) => {
    return Yup.mixed().test('custom-validation', errorMessage, validationFn);
  },
};

/**
 * Creates a validation schema from field definitions
 * @param {Array} fields - The fields definition with validation rules
 * @returns {Object} - Yup validation schema
 */
export const createValidationSchema = (fields) => {
  const schemaFields = {};

  fields.forEach((field) => {
    if (field.validations && field.validations.length > 0) {
      let fieldSchema;

      // Start with the appropriate base schema based on field type
      switch (field.type) {
        case 'number':
          fieldSchema = Yup.number().typeError('Must be a number');
          break;
        case 'date':
          fieldSchema = Yup.date().typeError('Must be a valid date');
          break;
        case 'boolean':
          fieldSchema = Yup.boolean();
          break;
        case 'array':
          fieldSchema = Yup.array();
          break;
        default:
          fieldSchema = Yup.string();
      }

      // Apply all validation rules
      field.validations.forEach((validation) => {
        if (typeof validation === 'string' && validationRules[validation]) {
          // Use predefined validation rule
          fieldSchema = validationRules[validation];
        } else if (typeof validation === 'object') {
          // Apply custom validation rule
          const { type, params, message } = validation;

          switch (type) {
            case 'required':
              fieldSchema = fieldSchema.required(message || 'This field is required');
              break;
            case 'min':
              fieldSchema = fieldSchema.min(
                params.value,
                message || `Minimum ${params.value} characters required`
              );
              break;
            case 'max':
              fieldSchema = fieldSchema.max(
                params.value,
                message || `Maximum ${params.value} characters allowed`
              );
              break;
            case 'email':
              fieldSchema = fieldSchema.email(message || 'Invalid email address');
              break;
            case 'matches':
              fieldSchema = fieldSchema.matches(params.regex, message || 'Invalid format');
              break;
            case 'oneOf':
              fieldSchema = fieldSchema.oneOf(params.values, message || 'Invalid value');
              break;
            case 'test':
              fieldSchema = fieldSchema.test(
                'custom-test',
                message || 'Validation failed',
                params.test
              );
              break;
            default:
              break;
          }
        }
      });

      schemaFields[field.name] = fieldSchema;
    }
  });

  return Yup.object().shape(schemaFields);
};

export default validationRules;

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab, Box, useTheme, useMediaQuery, Menu, MenuItem, Button } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

/**
 * Responsive tabs component that adapts to screen size
 * On smaller screens, it transforms into a dropdown menu
 *
 * @component
 */
const ResponsiveTabs = ({
  tabs,
  value,
  onChange,
  ariaLabel = 'tabs',
  tabColor = 'primary',
  variant = 'standard',
  orientation = 'horizontal',
  centered = false,
  scrollButtons = 'auto',
  selectionFollowsFocus = false,
  allowScrollButtonsMobile = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [showAsDropdown, setShowAsDropdown] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  // Check available space to determine if tabs should render as dropdown
  useEffect(() => {
    setShowAsDropdown(isMobile && tabs.length > 2);
  }, [isMobile, tabs.length]);

  // Handle dropdown menu open
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle dropdown menu close
  const handleClose = () => {
    setAnchorEl(null);
  };

  // Handle tab change via dropdown menu
  const handleMenuItemClick = (index) => {
    onChange(null, index);
    handleClose();
  };

  // Render as dropdown menu on small screens with many tabs
  if (showAsDropdown) {
    const selectedTab = tabs[value] || tabs[0];

    return (
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Button
          aria-controls={open ? 'tabs-menu' : undefined}
          aria-haspopup='true'
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          endIcon={<KeyboardArrowDownIcon />}
          fullWidth
          sx={{
            justifyContent: 'space-between',
            textAlign: 'left',
            py: 1,
            borderRadius: 0,
            borderBottom: `2px solid ${
              selectedTab.disabled ? theme.palette.action.disabled : theme.palette[tabColor].main
            }`,
          }}
        >
          {selectedTab.label}
        </Button>
        <Menu
          id='tabs-menu'
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'tabs-button',
            role: 'listbox',
            'aria-activedescendant': `tab-option-${value}`,
          }}
        >
          {tabs.map((tab, index) => (
            <MenuItem
              key={index}
              onClick={() => handleMenuItemClick(index)}
              selected={value === index}
              disabled={tab.disabled}
              id={`tab-option-${index}`}
              role='option'
              aria-selected={value === index}
            >
              {tab.label}
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }

  // Render regular tabs for larger screens
  return (
    <Box
      sx={{
        width: '100%',
        borderBottom: variant !== 'enclosed' ? 1 : 0,
        borderColor: 'divider',
        mb: 2,
      }}
    >
      <Tabs
        value={value}
        onChange={onChange}
        aria-label={ariaLabel}
        variant={tabs.length > 6 ? 'scrollable' : variant}
        scrollButtons={scrollButtons}
        allowScrollButtonsMobile={allowScrollButtonsMobile}
        orientation={orientation}
        centered={centered && tabs.length <= 6}
        selectionFollowsFocus={selectionFollowsFocus}
        textColor={tabColor}
        indicatorColor={tabColor}
        sx={{
          '& .MuiTabs-flexContainer': {
            flexWrap: isMobile ? 'wrap' : 'nowrap',
          },
          '& .MuiTab-root': {
            minWidth: isMobile ? 'auto' : 90,
            px: isMobile ? 1 : 2,
          },
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label}
            icon={tab.icon}
            iconPosition={tab.iconPosition || 'start'}
            disabled={tab.disabled}
            sx={tab.sx}
            id={`tab-${index}`}
            aria-controls={`tabpanel-${index}`}
          />
        ))}
      </Tabs>
    </Box>
  );
};

ResponsiveTabs.propTypes = {
  /** Array of tab objects with label, icon, iconPosition, disabled and sx properties */
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.node.isRequired,
      icon: PropTypes.node,
      iconPosition: PropTypes.oneOf(['start', 'end', 'top', 'bottom']),
      disabled: PropTypes.bool,
      sx: PropTypes.object,
    })
  ).isRequired,
  /** Current selected tab index */
  value: PropTypes.number.isRequired,
  /** Callback fired when a tab is selected - function(event, newValue) */
  onChange: PropTypes.func.isRequired,
  /** Aria label for the tabs component */
  ariaLabel: PropTypes.string,
  /** Color of the tabs */
  tabColor: PropTypes.oneOf(['primary', 'secondary', 'inherit']),
  /** Tabs appearance variant */
  variant: PropTypes.oneOf(['standard', 'scrollable', 'fullWidth', 'enclosed']),
  /** Tabs orientation */
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
  /** Whether tabs should be centered (only applies if variant is "standard") */
  centered: PropTypes.bool,
  /** When to show scroll buttons */
  scrollButtons: PropTypes.oneOf(['auto', 'desktop', 'never', true, false]),
  /** If true, pressing arrow keys changes focus without changing selected tab */
  selectionFollowsFocus: PropTypes.bool,
  /** If true, scroll buttons are shown on mobile */
  allowScrollButtonsMobile: PropTypes.bool,
};

export default ResponsiveTabs;

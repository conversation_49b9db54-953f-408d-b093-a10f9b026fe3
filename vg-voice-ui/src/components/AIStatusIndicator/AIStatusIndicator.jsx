import React from 'react';
import { Box, Typography, Chip, useTheme, keyframes } from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Phone,
  CheckCircle,
  Error,
  Schedule,
  SmartToy,
  VolumeUp,
} from '@mui/icons-material';

// Pulsing animation for active states
const pulse = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
`;

// Glowing animation for AI processing
const glow = keyframes`
  0% {
    box-shadow: 0 0 5px rgba(62, 219, 240, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(62, 219, 240, 0.8), 0 0 30px rgba(94, 74, 227, 0.4);
  }
  100% {
    box-shadow: 0 0 5px rgba(62, 219, 240, 0.5);
  }
`;

// Styled components
const StatusContainer = styled(Box)(({ theme, status }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  padding: theme.spacing(1.5, 2),
  borderRadius: 16,
  background: getStatusBackground(status, theme),
  border: `1px solid ${getStatusBorderColor(status, theme)}`,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    transition: 'left 0.5s',
  },
  
  '&:hover::before': {
    left: '100%',
  },
}));

const StatusIcon = styled(Box)(({ theme, status, isAnimated }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 32,
  height: 32,
  borderRadius: '50%',
  background: getIconBackground(status, theme),
  color: getIconColor(status, theme),
  animation: isAnimated ? `${status === 'ai_processing' ? glow : pulse} 2s infinite` : 'none',
  
  '& .MuiSvgIcon-root': {
    fontSize: '1.2rem',
  },
}));

const StatusChip = styled(Chip)(({ theme, status }) => ({
  borderRadius: 8,
  fontWeight: 600,
  fontSize: '0.75rem',
  height: 28,
  backgroundColor: getChipBackground(status, theme),
  color: getChipColor(status, theme),
  border: `1px solid ${getChipBorderColor(status, theme)}`,
  
  '& .MuiChip-label': {
    padding: '0 8px',
  },
}));

// Helper functions for colors
function getStatusBackground(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return 'linear-gradient(135deg, rgba(152, 245, 225, 0.1) 0%, rgba(102, 187, 106, 0.1) 100%)';
    case 'calling':
    case 'in_progress':
      return 'linear-gradient(135deg, rgba(94, 74, 227, 0.1) 0%, rgba(62, 219, 240, 0.1) 100%)';
    case 'ai_processing':
      return 'linear-gradient(135deg, rgba(62, 219, 240, 0.15) 0%, rgba(94, 74, 227, 0.15) 100%)';
    case 'failed':
    case 'error':
      return 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(255, 183, 77, 0.1) 100%)';
    default:
      return theme.palette.background.paper;
  }
}

function getStatusBorderColor(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.light;
    case 'calling':
    case 'in_progress':
      return theme.palette.primary.light;
    case 'ai_processing':
      return theme.palette.secondary.main;
    case 'failed':
    case 'error':
      return theme.palette.error.light;
    default:
      return theme.palette.divider;
  }
}

function getIconBackground(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.light;
    case 'calling':
    case 'in_progress':
      return theme.palette.primary.main;
    case 'ai_processing':
      return theme.palette.secondary.main;
    case 'failed':
    case 'error':
      return theme.palette.error.main;
    default:
      return theme.palette.grey[300];
  }
}

function getIconColor(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.contrastText;
    case 'calling':
    case 'in_progress':
    case 'ai_processing':
      return theme.palette.primary.contrastText;
    case 'failed':
    case 'error':
      return theme.palette.error.contrastText;
    default:
      return theme.palette.text.secondary;
  }
}

function getChipBackground(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.light;
    case 'calling':
    case 'in_progress':
      return theme.palette.primary.light;
    case 'ai_processing':
      return theme.palette.secondary.light;
    case 'failed':
    case 'error':
      return theme.palette.error.light;
    default:
      return theme.palette.grey[200];
  }
}

function getChipColor(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.dark;
    case 'calling':
    case 'in_progress':
      return theme.palette.primary.dark;
    case 'ai_processing':
      return theme.palette.secondary.dark;
    case 'failed':
    case 'error':
      return theme.palette.error.dark;
    default:
      return theme.palette.text.primary;
  }
}

function getChipBorderColor(status, theme) {
  switch (status) {
    case 'connected':
    case 'completed':
      return theme.palette.success.main;
    case 'calling':
    case 'in_progress':
      return theme.palette.primary.main;
    case 'ai_processing':
      return theme.palette.secondary.main;
    case 'failed':
    case 'error':
      return theme.palette.error.main;
    default:
      return theme.palette.grey[400];
  }
}

// Icon mapping
const getStatusIcon = (status) => {
  switch (status) {
    case 'connected':
    case 'completed':
      return <CheckCircle />;
    case 'calling':
    case 'in_progress':
      return <Phone />;
    case 'ai_processing':
      return <SmartToy />;
    case 'speaking':
      return <VolumeUp />;
    case 'failed':
    case 'error':
      return <Error />;
    default:
      return <Schedule />;
  }
};

// Status text mapping
const getStatusText = (status) => {
  switch (status) {
    case 'connected':
      return 'Connected';
    case 'completed':
      return 'Completed';
    case 'calling':
      return 'Calling';
    case 'in_progress':
      return 'In Progress';
    case 'ai_processing':
      return 'AI Processing';
    case 'speaking':
      return 'Speaking';
    case 'failed':
      return 'Failed';
    case 'error':
      return 'Error';
    default:
      return 'Unknown';
  }
};

/**
 * AI Status Indicator Component
 * Displays call status with futuristic animations and styling
 */
const AIStatusIndicator = ({ 
  status = 'unknown', 
  showText = true, 
  showChip = true, 
  animated = true,
  size = 'medium',
  customText,
  ...props 
}) => {
  const theme = useTheme();

  return (
    <StatusContainer status={status} {...props}>
      <StatusIcon status={status} isAnimated={animated}>
        {getStatusIcon(status)}
      </StatusIcon>
      
      {showText && (
        <Typography 
          variant={size === 'small' ? 'body2' : 'body1'} 
          sx={{ 
            fontWeight: 500,
            color: theme.palette.text.primary,
          }}
        >
          {customText || getStatusText(status)}
        </Typography>
      )}
      
      {showChip && (
        <StatusChip 
          status={status}
          label={customText || getStatusText(status)}
          size={size === 'small' ? 'small' : 'medium'}
        />
      )}
    </StatusContainer>
  );
};

export default AIStatusIndicator;

import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * List All Projects
 * GET /voice/api/v1/projects
 * @param {Object} params { skip, limit }
 */
export const listProjects = (params = {}) => {
  return apiService.get(apiUrl('/voice/api/v1/projects'), { params });
};

/**
 * Get Project by ID
 * GET /voice/api/v1/projects/{project_id}
 * @param {number} projectId
 */
export const getProjectById = (projectId) => {
  return apiService.get(apiUrl(`/voice/api/v1/projects/${projectId}`));
};

/**
 * Create Project
 * POST /voice/api/v1/projects
 * @param {Object} data { title, language, region }
 */
export const createProject = (data) => {
  return apiService.post(apiUrl('/voice/api/v1/projects'), data);
};

/**
 * Update Project
 * PUT /voice/api/v1/projects/{project_id}
 * @param {number} projectId
 * @param {Object} data
 */
export const updateProject = (projectId, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/projects/${projectId}`), data);
};

/**
 * Delete Project
 * DELETE /voice/api/v1/projects/{project_id}
 * @param {number} projectId
 */
export const deleteProject = (projectId) => {
  return apiService.delete(apiUrl(`/voice/api/v1/projects/${projectId}`));
};

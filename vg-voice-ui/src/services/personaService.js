import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * List all personas with pagination
 * @param {Object} options - Pagination options
 * @param {number} options.skip - Number of items to skip
 * @param {number} options.limit - Number of items to return
 * @param {number} options.project_id - Optional project ID to filter by
 * @returns {Promise} - Promise with the response data
 */
export const listPersonas = (options = {}) => {
  return apiService.get(apiUrl('/voice/api/v1/personas'), { params: options });
};

/**
 * Add a new persona
 * @param {Object} personaData - The persona data to add
 * @returns {Promise} - Promise with the response data
 */
export const createPersona = (personaData) => {
  return apiService.post(apiUrl('/voice/api/v1/personas'), personaData);
};

/**
 * Get all personas for a project
 * @param {number} projectId - The ID of the project to filter personas
 * @returns {Promise} - Promise with the response data
 */
export const getPersonas = (projectId) => {
  return apiService.get(apiUrl('/voice/api/v1/personas'), { params: { project_id: projectId } });
};

/**
 * Update an existing persona
 * @param {number} personaId - The ID of the persona to update
 * @param {Object} personaData - The updated persona data
 * @returns {Promise} - Promise with the response data
 */
export const updatePersona = (personaId, personaData) => {
  return apiService.put(apiUrl(`/voice/api/v1/personas/${personaId}`), personaData);
};

/**
 * Delete a persona
 * @param {number} personaId - The ID of the persona to delete
 * @returns {Promise} - Promise with the response data
 */
export const deletePersona = (personaId) => {
  return apiService.delete(apiUrl(`/voice/api/v1/personas/${personaId}`));
};

/**
 * Get all versions of a persona
 * @param {number} personaId - Persona ID
 * @returns {Promise} - Promise with the response data
 */
export const getPersonaVersions = (personaId) => {
  return apiService.get(apiUrl(`/voice/api/v1/personas/${personaId}/versions`));
};

/**
 * Create a new version of a persona
 * @param {number} personaId - Persona ID
 * @param {Object} data - Version data
 * @returns {Promise} - Promise with the response data
 */
export const createPersonaVersion = (personaId, data) => {
  return apiService.post(apiUrl(`/voice/api/v1/personas/${personaId}/versions`), data);
};

/**
 * Update a persona version
 * @param {number} versionId - Version ID
 * @param {Object} data - Updated version data
 * @returns {Promise} - Promise with the response data
 */
export const updatePersonaVersion = (versionId, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/personas/versions/${versionId}`), data);
};

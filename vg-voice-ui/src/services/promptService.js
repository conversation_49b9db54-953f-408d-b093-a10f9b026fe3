import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * List all prompts with pagination
 * @param {Object} params - Query parameters
 * @returns {Promise} - Promise with the response data
 */
export const listPrompts = (params = {}) => {
  return apiService.get(apiUrl('/voice/api/v1/prompts'), { params });
};

/**
 * Get a prompt by ID
 * @param {number} id - Prompt ID
 * @returns {Promise} - Promise with the response data
 */
export const getPrompt = (id) => {
  return apiService.get(apiUrl(`/voice/api/v1/prompts/${id}`));
};

/**
 * Create a new prompt
 * @param {Object} data - Prompt data
 * @returns {Promise} - Promise with the response data
 */
export const createPrompt = (data) => {
  return apiService.post(apiUrl('/voice/api/v1/prompts'), data);
};

/**
 * Update an existing prompt
 * @param {number} id - Prompt ID
 * @param {Object} data - Updated prompt data
 * @returns {Promise} - Promise with the response data
 */
export const updatePrompt = (id, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/prompts/${id}`), data);
};

/**
 * Delete a prompt
 * @param {number} id - Prompt ID
 * @returns {Promise} - Promise with the response data
 */
export const deletePrompt = (id) => {
  return apiService.delete(apiUrl(`/voice/api/v1/prompts/${id}`));
};

/**
 * Get all versions of a prompt
 * @param {number} promptId - Prompt ID
 * @returns {Promise} - Promise with the response data
 */
export const getPromptVersions = (promptId) => {
  return apiService.get(apiUrl(`/voice/api/v1/prompts/${promptId}/versions`));
};

/**
 * Create a new version of a prompt
 * @param {number} promptId - Prompt ID
 * @param {Object} data - Version data
 * @returns {Promise} - Promise with the response data
 */
export const createPromptVersion = (promptId, data) => {
  return apiService.post(apiUrl(`/voice/api/v1/prompts/${promptId}/versions`), data);
};

/**
 * Update a prompt version
 * @param {number} versionId - Version ID
 * @param {Object} data - Updated version data
 * @returns {Promise} - Promise with the response data
 */
export const updatePromptVersion = (versionId, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/prompts/versions/${versionId}`), data);
};

import { createTheme } from '@mui/material/styles';

// Create a custom AI-powered theme for the Vegrow Voice UI
const theme = createTheme({
  palette: {
    primary: {
      main: '#5E4AE3', // Deep indigo - AI intelligence and innovation
      light: '#8B7EE8',
      dark: '#4B0082',
      contrastText: '#fff',
    },
    secondary: {
      main: '#3EDBF0', // Electric cyan - tech-forward highlights
      light: '#6EE7F5',
      dark: '#00BFFF',
      contrastText: '#1E1E2F',
    },
    success: {
      main: '#66BB6A', // Mint green - softer success states
      light: '#98F5E1',
      dark: '#4CAF50',
      contrastText: '#fff',
    },
    warning: {
      main: '#FFB74D', // Soft amber for warnings
      light: '#FFCC80',
      dark: '#FF9800',
      contrastText: '#1E1E2F',
    },
    error: {
      main: '#f44336',
    },
    background: {
      default: '#F5F7FA', // Clean light background
      paper: '#FFFFFF',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // AI gradient
    },
    text: {
      primary: '#1E1E2F', // Strong legibility
      secondary: '#6E6E80', // Subtext/labels
    },
    // Custom AI theme colors
    ai: {
      primary: '#5E4AE3',
      accent: '#3EDBF0',
      success: '#98F5E1',
      warning: '#FFB74D',
      gradient: {
        primary: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
        secondary: 'linear-gradient(135deg, #98F5E1 0%, #66BB6A 100%)',
        dark: 'linear-gradient(135deg, #1E1E2F 0%, #4B0082 100%)',
      },
    },
  },
  typography: {
    fontFamily: ['Roboto', '"Helvetica Neue"', 'Arial', 'sans-serif'].join(','),
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 600,
    h1: {
      fontSize: '2.2rem',
      fontWeight: 600,
      lineHeight: 1.2,
      letterSpacing: '-0.01em',
    },
    h2: {
      fontSize: '1.8rem',
      fontWeight: 600,
      lineHeight: 1.3,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.3,
      letterSpacing: 0,
    },
    h4: {
      fontSize: '1.2rem',
      fontWeight: 600,
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    h5: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    h6: {
      fontSize: '0.9rem',
      fontWeight: 600,
      lineHeight: 1.4,
      letterSpacing: 0,
    },
    subtitle1: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.75,
      letterSpacing: '0.02em',
      textTransform: 'none',
    },
    caption: {
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.01em',
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.08em',
      textTransform: 'uppercase',
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          borderRadius: 12, // Rounded elements
          textTransform: 'none',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 8px 25px rgba(94, 74, 227, 0.3)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #5E4AE3 0%, #3EDBF0 100%)',
          boxShadow: '0 4px 15px rgba(94, 74, 227, 0.4)',
          '&:hover': {
            background: 'linear-gradient(135deg, #4B0082 0%, #00BFFF 100%)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          marginBottom: 16,
          '& .MuiOutlinedInput-root': {
            borderRadius: 12,
            transition: 'all 0.3s ease',
            '&:hover': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#3EDBF0',
              },
            },
            '&.Mui-focused': {
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#5E4AE3',
                boxShadow: '0 0 0 3px rgba(94, 74, 227, 0.1)',
              },
            },
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          WebkitFontSmoothing: 'antialiased',
          MozOsxFontSmoothing: 'grayscale',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16, // Rounded elements
          boxShadow: '0 8px 32px rgba(94, 74, 227, 0.1)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          border: '1px solid rgba(94, 74, 227, 0.1)',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 16px 48px rgba(94, 74, 227, 0.2)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0 8px 32px rgba(94, 74, 227, 0.08)',
          border: '1px solid rgba(94, 74, 227, 0.05)',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          fontSize: '0.875rem',
          backgroundColor: 'rgba(94, 74, 227, 0.05)',
        },
        body: {
          fontSize: '0.875rem',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
        },
      },
    },
  },
});

export default theme;

## Product Requirements Document (PRD)

**Product Name: Vegrow Voice – AI Calling Platform
Owner: <PERSON>tha
Version: 1.
Date: 13 May, 2025
Tentative Suggested Application Domains:** calling.vegrow.in or voice.vegrow.in
The above Image is purely for reference. Nothing concrete. Will update the details soon..
Timeline: Vegrow Voice: Timelines

# 1. Objective

To build a scalable, modular AI Calling Platform (Vegrow Voice) that allows internal users to trigger
structured AI-powered voice calls via projects. Each project is tied to a specific conversational goal (e.g.,
profiling, survey, awareness) and contains configurable prompts, languages, and batch call triggers via
CSV upload.
The platform is extensible to future use cases like marketing, support, feedback collection, and fully
integrates structured call outputs into downstream analytics.

# Why Now?

```
● Internal demand across supply, ops, and marketing teams
```

```
● Multiple LLMs and AI voice capabilities now mature
● Structured voice-data → Strategic analytics leverage
● Building foundational infra to power future AI-native workflows
```
## 2. Design & UX Principles (Tentative)

```
● Theme futuristic (Dark/Light - mode switch planned)
● Font: Orbitron / Roboto Condensed (sans-serif, no curves)
● Colors:
○ Background: #000000 (<PERSON>)
○ Text: #FFFFFF (White)
○ Accent: #00FF7F (Neon Green)
● Interaction: Smooth transitions, high-contrast layout, minimal UI, real-time feedback
```
# 3. Problem Statement

Current AI call systems are rigid, siloed, and hard to configure. Vegrow Voice addresses:
● Multi-use-case support
● Model flexibility (Gemini, Bland, GPT)
● Reusable, configurable prompts
● Clear status tracking
● Structured data integration for decision-making

# 4. Scope – Phase 1 (Farmer Profiling)

```
● Trigger calls individual/batch via CSV import
● Use pre-linked questionnaires per project, project owner should be able to configure these
questions against a project
● Capture structured answers (JSON + transcript), Summary prompts are configured for a project
● Dashboard to monitor call health & profiling success
● For the initial phase, we will have only an admin role -> means admin can perform all actions
including project creation.
```

## 5. Core Modules

**A. Configuration Layer**
● **Projects / Use Cases**
○ Each project is an independent unit with its **own prompts, own questionnaire** , and **own
call batches**.
○ Title, Language, Voice, Engine, Linked Questionnaire, Region, number configuration
(Future Integration)
○ List of Questionnaire against a project that needs to be extracted from a conversation
○ Example:
■ Project Title: _Farmer Profiling – Nashik (Summer 2025)
■ Language: Hindi
■ Voice Persona: Priyanka_
○ We will have **multiple prompts per project** , classified by **type** and **version**
○ **Questionnaire – Local to Each Project**
○ Every project will maintain its own questionnaire_config — ensuring flexibility, isolation,
and ease of versioning.
**Advantages:
Feature Benefit**
Multiple prompt versions Run A/B tests, iterate quickly
Prompt tied to engine/model Plug & play with Gemini, Bland, GPT-
Project-specific questionnaire Prevents question pollution across domains
Flexibility for future scaling Custom use cases with their own logic
● **Persona Definition:**
○ Prompt, Voice, Engine (Eg: Gemini Flash 2.5, etc )
○ Against each persona, we can have multiple versions and everything remains the same
as the current setup of prompt management.
○ This gives the ability to automate the testing, benchmarking.

##### Persona refers to the AI caller’s voice identity — including name, tone, language, and

##### speaking style — designed to create a natural, relatable, and contextual experience for the


##### listener. Each project can define one or more personas to suit regional or use-case-specific

##### needs with different versions to it.

● **Prompt Manager in a project**
○ Conversational Prompt (used in call) **(High priority)**
○ Summary Prompt (post-call analysis)
○ We will configure more types of prompts based on the use.
○ Fallbacks, Variables, Prompt Versions
○ Note: Make use of existing prompt versioning setup and map it to a project.
**_Note_** _: We will all the prompts manager, questionnaire, analytics, post processed metrics in a generic
table but mapped to a project_

#### B. Execution Layer

**1. Call Triggering UI**
    ● Supports two modes:
       - **Default Call** : Trigger with project’s preset config
       - **Custom Call** : Select prompt version, persona, engine
    ● CSV upload enabled to import the data; future plan to fetch/push data via APIs (e.g., Farmers
       data from Velynk and push the post call data to Velynk)
**2. Call Engine Routing**
    ● Current: **Gemini 2.5 Flash (Audio-to-Audio)**
    ● Future: **Bland AI (voice cloning)**
    ● Engine is configurable per prompt/project
**3. Live Monitoring**
    ● Track progress, retry/cancel individual calls
    ● Show queue status, response logs
**4. Response Capture**
    ● Captures: call_id, engine, persona, audio, transcript, JSON output, sentiment
    ● Stored for analytics and downstream workflows


**C. Insights Layer**
● **Dashboards**
○ Summary: Total / Completed / Failed / In Progress
○ Trendline: 7-day, engine-wise
○ Sentiment and profiling success stats
● **Analytics**
○ Question drop-off, avg duration, sentiment distribution
○ JSON extraction rate
○ A/B prompt success comparison
● **Export Support**
○ Call results & questionnaire answers → CSV / JSON

# 6. UI & UX Design

**Navigation**
● Sidebar/Topbar: Home, Call Logs, Analytics, Settings

# Navigation Modules

```
● Home/Dashboard
○ Show the list of calls - success/failure/not connected rate
○ Hero: "Vegrow Voice"
○ Cards: Calls Triggered / Completed / Failed / In Progress
○ Chart: 7-Day Trendline
○ Quick CTA: "+ Start Profiling Call"
● Call Logs
○ Option to trigger individual call
○ Option to trigger batch calls
■ Ability to import through CSV
■ Option to fetch the farmer data through API (Future Scope)
■ Filterable Call Batch List
■ Batch Details: Status, Progress, Prompt Used
```

```
■ Trigger Batch Call: Select Persona + Upload CSV
○ Call logs listing page
■ Headers: Sign to show Incoming/Outgoing, Call ID, To, From, Persona, Call
Status, Conversation Status, Call logs (Duration, Transcript, Recording), Tags,
Remarks, Summary
■ On clicking on the row, users should be able to see more detailed information
like logs, error notes, all the meta info.
● Call Response Data
○ Listing page with the answers against questionnaire defined
○ Against an identifier, user should be able to answers to the question
● Setting
○ Persona’s setup
■ Conversation Prompt, voice, language, model - persona
■ Persona can have multiple versions
○ Summary Prompt to extract the structured data to answer the questionnaire
○ Questionnaire Setup for the project
■ Whenever a call goes, an instance of a question is stored and the answer that we
get the structured format.
○ User roles
■ For this release, we will have admin access for all the users.
■ In Future phases, we will bring RBAC
■ Global admin will have access to create new projects.
```
# 7. Data Models

```
● Projects
● Prompts (conversation + summary + Other Types)
● Personas
● Questionnaires
● Calls
● Call Responses (linked to questionnaire)
```

### 8. User Journeys

#### A. Project Owner

```
● Logs in → Dashboard
● Creates project → sets language, model, voice
● Configures questionnaire and prompts
● Uploads farmer list (CSV)
● Triggers batch calls → views results in dashboard
```
#### B. Internal Tester

```
● Selects custom prompt/persona/model
● Triggers test call
● Reviews transcript + structured JSON
```
## 9. Technical Stack

```
● Frontend: React.js + TailwindCSS (Dark theme default)
● Backend: FastAPI, MySQL (structured), MongoDB (transcripts & metrics)
● Engine: Bland, Custom (Gemini)
● LLMs: OpenAI GPT-4, Whisper, Gemini 2.5 Flash
● Storage: AWS S3 (audio, JSON)
● Auth: JWT-based, RBAC (planned)
● Monitoring: Sentry, structured JSON logs
```
## 10. Success Metrics

**Metric Description**
Call Completion % Connected + Answered / Total
Data Extraction Accuracy # of structured fields / total expected
Avg Call Duration Duration of successful calls
Sentiment Distribution Positive / Neutral / Negative
Prompt Version Success Delta A/B improvement over time


Re-call Rate % of calls needing fallback

## 11. Future Scope

```
● API Integration with Velynk and other services
● Multilingual Fallback Handling
● Voice Cloning
● Real time Transcription
● RBAC for multi-role workflows
```
## 12. Metrics Definition (Will work)

```
Work in Progress
Final Notes:
● Admin users have global access in Phase 1
● Projects are the unit of abstraction
● Prompt/questionnaire/performance data is mapped per project
● Generic prompt/questionnaire/metrics tables scoped by project ID
● Designed for long-term extensibility and plug-in AI engine routing
```


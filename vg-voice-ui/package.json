{"name": "vg-voice-ui", "version": "0.1.0", "private": true, "dependencies": {"@date-io/dayjs": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.0", "@react-oauth/google": "^0.12.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "plugin:storybook/recommended", "plugin:prettier/recommended"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"]}, "devDependencies": {"@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/preset-create-react-app": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-storybook": "^0.12.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prop-types": "^15.8.1", "storybook": "^8.6.12", "webpack": "^5.99.8"}}